<?php
/**
 * حذف الوثيقة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('delete_document')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذه العملية']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

// قراءة البيانات من JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['document_id']) || empty($input['document_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الوثيقة مطلوب']);
    exit;
}

$document_id = (int)$input['document_id'];

try {
    $db = Database::getInstance();
    
    // جلب تفاصيل الوثيقة قبل الحذف
    $document = $db->fetchOne("
        SELECT d.*, 
               dt.type_name_arabe,
               e.nom, e.prenom, e.matricule
        FROM documents d
        JOIN document_types dt ON d.document_type_id = dt.id
        JOIN employees e ON d.employee_id = e.id
        WHERE d.id = ?
    ", [$document_id]);
    
    if (!$document) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الوثيقة غير موجودة']);
        exit;
    }
    
    // التحقق من صلاحية حذف الوثيقة
    if (!canDeleteDocument($document)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'غير مصرح لك بحذف هذه الوثيقة']);
        exit;
    }
    
    // بدء المعاملة
    $db->beginTransaction();
    
    try {
        // حذف الوثيقة من قاعدة البيانات
        $deleted = $db->delete('documents', ['id' => $document_id]);
        
        if (!$deleted) {
            throw new Exception('فشل في حذف الوثيقة من قاعدة البيانات');
        }
        
        // حذف الملف الفعلي
        $file_path = UPLOAD_PATH . $document['file_path'];
        if (file_exists($file_path)) {
            if (!unlink($file_path)) {
                // تسجيل تحذير ولكن لا نوقف العملية
                error_log("تحذير: فشل في حذف الملف: $file_path");
            }
        }
        
        // حذف الصورة المصغرة إن وجدت
        $thumbnail_path = dirname($file_path) . '/thumbnails/thumb_' . basename($file_path);
        if (file_exists($thumbnail_path)) {
            unlink($thumbnail_path);
        }
        
        // تسجيل العملية في سجل المراجعة
        logActivity(
            'document_deleted',
            'documents',
            $document_id,
            [
                'employee_id' => $document['employee_id'],
                'employee_name' => $document['nom'] . ' ' . $document['prenom'],
                'employee_matricule' => $document['matricule'],
                'document_type' => $document['type_name_arabe'],
                'filename' => $document['filename'],
                'original_filename' => $document['original_filename'],
                'file_size' => formatFileSize($document['file_size']),
                'upload_date' => $document['upload_date']
            ]
        );
        
        // تأكيد المعاملة
        $db->commit();
        
        // إرسال إشعار (إذا كان مفعلاً)
        if (NOTIFICATIONS_ENABLED) {
            sendNotification([
                'type' => 'document_deleted',
                'title' => 'تم حذف وثيقة',
                'message' => "تم حذف وثيقة {$document['type_name_arabe']} للموظف {$document['nom']} {$document['prenom']}",
                'user_id' => $_SESSION['user_id'],
                'related_id' => $document_id,
                'related_type' => 'document'
            ]);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الوثيقة بنجاح',
            'data' => [
                'document_id' => $document_id,
                'employee_name' => $document['nom'] . ' ' . $document['prenom'],
                'document_type' => $document['type_name_arabe'],
                'filename' => $document['filename']
            ]
        ]);
        
    } catch (Exception $e) {
        // التراجع عن المعاملة
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("خطأ في حذف الوثيقة: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * التحقق من صلاحية حذف الوثيقة
 */
function canDeleteDocument($document) {
    // المدير يمكنه حذف جميع الوثائق
    if (hasRole('admin')) {
        return true;
    }
    
    // مسؤول الموارد البشرية يمكنه حذف جميع الوثائق
    if (hasRole('hr_manager')) {
        return true;
    }
    
    // موظف الموارد البشرية يمكنه حذف الوثائق التي رفعها
    if (hasRole('hr_employee')) {
        return $document['uploaded_by'] == $_SESSION['user_id'];
    }
    
    // مسؤول الأرشيف يمكنه حذف الوثائق التي رفعها
    if (hasRole('archivist')) {
        return $document['uploaded_by'] == $_SESSION['user_id'];
    }
    
    return false;
}

/**
 * إرسال إشعار
 */
function sendNotification($notification_data) {
    try {
        // يمكن إضافة منطق الإشعارات هنا
        // مثل إرسال بريد إلكتروني أو إشعار في النظام
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
        return false;
    }
}
?>

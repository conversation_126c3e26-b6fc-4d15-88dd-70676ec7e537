<?php
/**
 * صفحة إدارة الموظفين
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('view_employees')) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$page_title = 'إدارة الموظفين';

// معالجة البحث والفلاتر
$search_params = [
    'search' => $_GET['search'] ?? '',
    'structure' => $_GET['structure'] ?? '',
    'grade' => $_GET['grade'] ?? '',
    'status' => $_GET['status'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'sort' => $_GET['sort'] ?? 'nom',
    'order' => $_GET['order'] ?? 'ASC',
    'page' => (int)($_GET['page'] ?? 1),
    'per_page' => (int)($_GET['per_page'] ?? 20)
];

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search_params['search'])) {
    $search_term = '%' . $search_params['search'] . '%';
    $where_conditions[] = "(nom LIKE ? OR prenom LIKE ? OR matricule LIKE ? OR email LIKE ?)";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
}

if (!empty($search_params['structure'])) {
    $where_conditions[] = "structure = ?";
    $params[] = $search_params['structure'];
}

if (!empty($search_params['grade'])) {
    $where_conditions[] = "grade = ?";
    $params[] = $search_params['grade'];
}

if (!empty($search_params['status'])) {
    $where_conditions[] = "status = ?";
    $params[] = $search_params['status'];
}

if (!empty($search_params['date_from'])) {
    $where_conditions[] = "date_recrutement >= ?";
    $params[] = $search_params['date_from'];
}

if (!empty($search_params['date_to'])) {
    $where_conditions[] = "date_recrutement <= ?";
    $params[] = $search_params['date_to'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// حساب إجمالي النتائج
$total_query = "SELECT COUNT(*) FROM employees $where_clause";
$total_employees = $db->fetchValue($total_query, $params);

// حساب الصفحات
$total_pages = ceil($total_employees / $search_params['per_page']);
$offset = ($search_params['page'] - 1) * $search_params['per_page'];

// جلب الموظفين
$order_clause = "ORDER BY {$search_params['sort']} {$search_params['order']}";
$limit_clause = "LIMIT {$search_params['per_page']} OFFSET $offset";

$employees_query = "
    SELECT e.*, 
           COUNT(d.id) as documents_count,
           MAX(d.upload_date) as last_document_date
    FROM employees e
    LEFT JOIN documents d ON e.id = d.employee_id AND d.status != 'deleted'
    $where_clause
    GROUP BY e.id
    $order_clause
    $limit_clause
";

$employees = $db->fetchAll($employees_query, $params);

// جلب قوائم الفلاتر
$structures = $db->fetchAll("SELECT DISTINCT structure FROM employees WHERE structure IS NOT NULL ORDER BY structure");
$grades = $db->fetchAll("SELECT DISTINCT grade FROM employees WHERE grade IS NOT NULL ORDER BY grade");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام إدارة الوثائق</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .search-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            color: white;
        }
        
        .employee-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .employee-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .employee-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-suspended { background: #fff3cd; color: #856404; }
        
        .filter-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
        }
        
        .filter-form .form-control,
        .filter-form .form-select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .pagination-wrapper {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .quick-stats {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-arrow-left"></i> العودة للوحة التحكم
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-people"></i> <?php echo $page_title; ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($total_employees); ?></div>
                        <div class="stat-label">إجمالي النتائج</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format(count($structures)); ?></div>
                        <div class="stat-label">الهياكل</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format(count($grades)); ?></div>
                        <div class="stat-label">الدرجات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $total_pages; ?></div>
                        <div class="stat-label">الصفحات</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة البحث والفلاتر -->
        <div class="search-panel">
            <h5><i class="bi bi-search"></i> البحث والفلاتر المتقدمة</h5>
            
            <form method="GET" class="filter-form">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">البحث العام</label>
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($search_params['search']); ?>"
                               placeholder="الاسم، رقم التسجيل، البريد الإلكتروني...">
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الهيكل</label>
                        <select class="form-select" name="structure">
                            <option value="">جميع الهياكل</option>
                            <?php foreach ($structures as $structure): ?>
                            <option value="<?php echo htmlspecialchars($structure['structure']); ?>"
                                    <?php echo $search_params['structure'] === $structure['structure'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($structure['structure']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الدرجة</label>
                        <select class="form-select" name="grade">
                            <option value="">جميع الدرجات</option>
                            <?php foreach ($grades as $grade): ?>
                            <option value="<?php echo htmlspecialchars($grade['grade']); ?>"
                                    <?php echo $search_params['grade'] === $grade['grade'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($grade['grade']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo $search_params['status'] === 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $search_params['status'] === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            <option value="suspended" <?php echo $search_params['status'] === 'suspended' ? 'selected' : ''; ?>>معلق</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label class="form-label">تاريخ التوظيف</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="date" class="form-control" name="date_from" 
                                       value="<?php echo $search_params['date_from']; ?>" placeholder="من">
                            </div>
                            <div class="col-6">
                                <input type="date" class="form-control" name="date_to" 
                                       value="<?php echo $search_params['date_to']; ?>" placeholder="إلى">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">ترتيب حسب</label>
                        <select class="form-select" name="sort">
                            <option value="nom" <?php echo $search_params['sort'] === 'nom' ? 'selected' : ''; ?>>الاسم</option>
                            <option value="matricule" <?php echo $search_params['sort'] === 'matricule' ? 'selected' : ''; ?>>رقم التسجيل</option>
                            <option value="date_recrutement" <?php echo $search_params['sort'] === 'date_recrutement' ? 'selected' : ''; ?>>تاريخ التوظيف</option>
                            <option value="structure" <?php echo $search_params['sort'] === 'structure' ? 'selected' : ''; ?>>الهيكل</option>
                            <option value="grade" <?php echo $search_params['sort'] === 'grade' ? 'selected' : ''; ?>>الدرجة</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الاتجاه</label>
                        <select class="form-select" name="order">
                            <option value="ASC" <?php echo $search_params['order'] === 'ASC' ? 'selected' : ''; ?>>تصاعدي</option>
                            <option value="DESC" <?php echo $search_params['order'] === 'DESC' ? 'selected' : ''; ?>>تنازلي</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">عدد النتائج</label>
                        <select class="form-select" name="per_page">
                            <option value="10" <?php echo $search_params['per_page'] === 10 ? 'selected' : ''; ?>>10</option>
                            <option value="20" <?php echo $search_params['per_page'] === 20 ? 'selected' : ''; ?>>20</option>
                            <option value="50" <?php echo $search_params['per_page'] === 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo $search_params['per_page'] === 100 ? 'selected' : ''; ?>>100</option>
                        </select>
                    </div>
                    
                    <div class="col-md-5 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-light me-2">
                            <i class="bi bi-search"></i> بحث
                        </button>
                        <a href="employees.php" class="btn btn-outline-light me-2">
                            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                        </a>
                        <button type="button" class="btn btn-outline-light" onclick="exportResults()">
                            <i class="bi bi-download"></i> تصدير
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- قائمة الموظفين -->
        <div class="row">
            <?php if (empty($employees)): ?>
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="bi bi-info-circle"></i> لا توجد نتائج تطابق معايير البحث
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($employees as $employee): ?>
            <div class="col-md-6 col-lg-4">
                <div class="employee-card card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="employee-avatar me-3">
                                <?php echo strtoupper(substr($employee['nom'], 0, 1) . substr($employee['prenom'], 0, 1)); ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">
                                    <?php echo htmlspecialchars($employee['nom'] . ' ' . $employee['prenom']); ?>
                                </h6>
                                <small class="text-muted">رقم التسجيل: <?php echo htmlspecialchars($employee['matricule']); ?></small>
                            </div>
                            <span class="status-badge status-<?php echo $employee['status'] ?? 'active'; ?>">
                                <?php echo getStatusText($employee['status'] ?? 'active'); ?>
                            </span>
                        </div>
                        
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">الهيكل:</small><br>
                                <small><?php echo htmlspecialchars($employee['structure'] ?? 'غير محدد'); ?></small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">الدرجة:</small><br>
                                <small><?php echo htmlspecialchars($employee['grade'] ?? 'غير محدد'); ?></small>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">الوثائق:</small><br>
                                <small><i class="bi bi-file-earmark-text text-primary"></i> <?php echo $employee['documents_count']; ?></small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">آخر وثيقة:</small><br>
                                <small>
                                    <?php if ($employee['last_document_date']): ?>
                                        <?php echo timeAgo($employee['last_document_date']); ?>
                                    <?php else: ?>
                                        لا توجد وثائق
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <div class="btn-group btn-group-sm">
                                <a href="employee_profile.php?id=<?php echo $employee['id']; ?>" 
                                   class="btn btn-outline-primary" title="عرض الملف">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <?php if (hasPermission('edit_employee')): ?>
                                <a href="employee_edit.php?id=<?php echo $employee['id']; ?>" 
                                   class="btn btn-outline-warning" title="تحرير">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <?php endif; ?>
                                <a href="document_scanner.php?employee_id=<?php echo $employee['id']; ?>" 
                                   class="btn btn-outline-success" title="إدارة الوثائق">
                                    <i class="bi bi-file-earmark-plus"></i>
                                </a>
                            </div>
                            
                            <small class="text-muted align-self-center">
                                <?php if ($employee['date_recrutement']): ?>
                                    منذ <?php echo date('Y', strtotime($employee['date_recrutement'])); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- التنقل بين الصفحات -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination-wrapper">
            <nav aria-label="تنقل الصفحات">
                <ul class="pagination justify-content-center mb-0">
                    <!-- الصفحة السابقة -->
                    <?php if ($search_params['page'] > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo buildPaginationUrl($search_params, $search_params['page'] - 1); ?>">
                            <i class="bi bi-chevron-right"></i> السابق
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <!-- أرقام الصفحات -->
                    <?php
                    $start_page = max(1, $search_params['page'] - 2);
                    $end_page = min($total_pages, $search_params['page'] + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <li class="page-item <?php echo $i === $search_params['page'] ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo buildPaginationUrl($search_params, $i); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                    
                    <!-- الصفحة التالية -->
                    <?php if ($search_params['page'] < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo buildPaginationUrl($search_params, $search_params['page'] + 1); ?>">
                            التالي <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="text-center mt-2">
                <small class="text-muted">
                    عرض <?php echo (($search_params['page'] - 1) * $search_params['per_page'] + 1); ?> - 
                    <?php echo min($search_params['page'] * $search_params['per_page'], $total_employees); ?> 
                    من أصل <?php echo number_format($total_employees); ?> موظف
                </small>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تصدير النتائج
        function exportResults() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'excel');
            window.location.href = 'export_employees.php?' + params.toString();
        }
        
        // تحديث الصفحة عند تغيير عدد النتائج
        document.querySelector('select[name="per_page"]').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</body>
</html>

<?php
// دوال مساعدة
function getStatusText($status) {
    switch ($status) {
        case 'active': return 'نشط';
        case 'inactive': return 'غير نشط';
        case 'suspended': return 'معلق';
        default: return 'غير محدد';
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    
    return date('Y-m-d', strtotime($datetime));
}

function buildPaginationUrl($params, $page) {
    $params['page'] = $page;
    return '?' . http_build_query($params);
}
?>

# نظام إدارة الوثائق الإلكترونية للموظفين
# ملف الحماية والتوجيه

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية ملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تعيين صفحات الأخطاء المخصصة
ErrorDocument 403 /Edocs/pages/error.php?code=403
ErrorDocument 404 /Edocs/pages/error.php?code=404
ErrorDocument 500 /Edocs/pages/error.php?code=500

# تحسين الأداء - ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين الأداء - تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبتات الضارة
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# حد أقصى لحجم الملفات المرفوعة
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# تعيين المنطقة الزمنية
php_value date.timezone "Africa/Algiers"

# تفعيل عرض الأخطاء في بيئة التطوير فقط
# php_flag display_errors Off
# php_flag log_errors On
# php_value error_log /path/to/error.log

# إعادة توجيه الصفحة الرئيسية
DirectoryIndex index.php index.html

# قواعد إعادة التوجيه
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# إعادة توجيه الروابط الودية (إذا لزم الأمر لاحقاً)
# RewriteRule ^employee/([0-9]+)/?$ pages/employee_profile.php?id=$1 [L,QSA]
# RewriteRule ^search/?$ pages/search.php [L,QSA]

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<Directory "includes">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

<Directory "config">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

# السماح بالوصول لملفات معينة فقط
<Directory "includes">
    <Files "import_excel.php">
        Order Allow,Deny
        Allow from all
    </Files>
</Directory>

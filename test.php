<?php
echo "PHP يعمل بشكل صحيح!<br>";
echo "الإصدار: " . PHP_VERSION . "<br>";
echo "التاريخ: " . date('Y-m-d H:i:s') . "<br>";

// اختبار الاتصال بـ MySQL
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "الاتصال بـ MySQL: ✅ نجح<br>";
    echo "إصدار MySQL: " . $pdo->query('SELECT VERSION()')->fetchColumn() . "<br>";
} catch (Exception $e) {
    echo "الاتصال بـ MySQL: ❌ فشل - " . $e->getMessage() . "<br>";
}

// اختبار قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=localhost;dbname=dossier_employes", "root", "");
    echo "قاعدة البيانات dossier_employes: ✅ موجودة<br>";
} catch (Exception $e) {
    echo "قاعدة البيانات dossier_employes: ❌ غير موجودة<br>";
}

echo "<br><a href='setup.php'>الذهاب لصفحة الإعداد</a>";
?>

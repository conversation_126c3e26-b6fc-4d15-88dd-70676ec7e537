<?php
/**
 * جلب تفاصيل الوثيقة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('view_documents')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذه العملية']);
    exit;
}

// التحقق من معرف الوثيقة
$document_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($document_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الوثيقة غير صحيح']);
    exit;
}

try {
    $db = Database::getInstance();
    
    // جلب تفاصيل الوثيقة
    $document = $db->fetchOne("
        SELECT d.*, 
               dt.type_name, dt.type_name_arabe, dt.category, dt.description,
               e.nom, e.prenom, e.matricule, e.structure,
               u.full_name as uploaded_by_name
        FROM documents d
        JOIN document_types dt ON d.document_type_id = dt.id
        JOIN employees e ON d.employee_id = e.id
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.id = ?
    ", [$document_id]);
    
    if (!$document) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الوثيقة غير موجودة']);
        exit;
    }
    
    // التحقق من صلاحية الوصول للوثيقة
    if (!canAccessDocument($document)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول لهذه الوثيقة']);
        exit;
    }
    
    // التحقق من وجود الملف
    $file_path = UPLOAD_PATH . $document['file_path'];
    if (!file_exists($file_path)) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'ملف الوثيقة غير موجود']);
        exit;
    }
    
    // معلومات إضافية عن الملف
    $file_info = [
        'size_formatted' => formatFileSize($document['file_size']),
        'upload_date_formatted' => date('d/m/Y H:i', strtotime($document['upload_date'])),
        'file_exists' => true,
        'is_image' => in_array($document['file_type'], ['image/jpeg', 'image/jpg', 'image/png']),
        'is_pdf' => $document['file_type'] === 'application/pdf'
    ];
    
    // فك تشفير البيانات الوصفية
    $metadata = [];
    if (!empty($document['metadata'])) {
        $metadata = json_decode($document['metadata'], true) ?: [];
    }
    
    // تسجيل عملية العرض
    logActivity(
        'document_viewed',
        'documents',
        $document_id,
        [
            'employee_id' => $document['employee_id'],
            'employee_name' => $document['nom'] . ' ' . $document['prenom'],
            'document_type' => $document['type_name_arabe'],
            'filename' => $document['filename']
        ]
    );
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'document' => array_merge($document, $file_info),
        'metadata' => $metadata,
        'employee' => [
            'id' => $document['employee_id'],
            'name' => $document['nom'] . ' ' . $document['prenom'],
            'matricule' => $document['matricule'],
            'structure' => $document['structure']
        ],
        'document_type' => [
            'id' => $document['document_type_id'],
            'name' => $document['type_name'],
            'name_arabic' => $document['type_name_arabe'],
            'category' => $document['category'],
            'description' => $document['description']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("خطأ في جلب الوثيقة: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب الوثيقة'
    ]);
}

/**
 * التحقق من صلاحية الوصول للوثيقة
 */
function canAccessDocument($document) {
    // المدير يمكنه الوصول لجميع الوثائق
    if (hasRole('admin')) {
        return true;
    }
    
    // مسؤول الموارد البشرية يمكنه الوصول لجميع الوثائق
    if (hasRole('hr_manager')) {
        return true;
    }
    
    // موظف الموارد البشرية يمكنه الوصول للوثائق العامة
    if (hasRole('hr_employee')) {
        return true;
    }
    
    // مسؤول الأرشيف يمكنه الوصول للوثائق
    if (hasRole('archivist')) {
        return true;
    }
    
    // المستخدم العادي يمكنه الوصول للوثائق العامة فقط
    if (hasRole('viewer')) {
        // يمكن إضافة منطق إضافي هنا للتحكم في الوصول
        return true;
    }
    
    return false;
}
?>

-- إنشاء جداول قاعدة البيانات
-- نظام إدارة الوثائق الإلكترونية للموظفين

USE dossier_employes;

-- 1. جدول المستخدمين (يجب إنشاؤه أولاً للمراجع الخارجية)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT 'اسم المستخدم',
    email VARCHAR(100) UNIQUE COMMENT 'البريد الإلكتروني',
    password_hash VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
    
    -- معلومات شخصية
    full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل',
    full_name_arabe VARCHAR(100) COMMENT 'الاسم الكامل بالعربية',
    
    -- صلاحيات
    role ENUM('admin', 'hr_manager', 'hr_employee', 'archivist', 'viewer') 
         NOT NULL DEFAULT 'viewer' COMMENT 'دور المستخدم',
    permissions JSON COMMENT 'صلاحيات مخصصة',
    
    -- معلومات الحساب
    is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الحساب نشط',
    last_login TIMESTAMP NULL COMMENT 'آخر تسجيل دخول',
    failed_login_attempts INT DEFAULT 0 COMMENT 'محاولات تسجيل الدخول الفاشلة',
    locked_until TIMESTAMP NULL COMMENT 'مقفل حتى',
    
    -- معلومات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ الحساب',
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='جدول المستخدمين';

-- 2. جدول الموظفين
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_number VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم الموظف',
    nin VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم الهوية الوطنية',
    matricule VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم التسجيل',
    
    -- المعلومات الشخصية
    nom VARCHAR(100) NOT NULL COMMENT 'اللقب',
    prenom VARCHAR(100) NOT NULL COMMENT 'الاسم',
    nom_arabe VARCHAR(100) COMMENT 'اللقب بالعربية',
    prenom_arabe VARCHAR(100) COMMENT 'الاسم بالعربية',
    
    -- معلومات الميلاد
    date_naissance DATE COMMENT 'تاريخ الميلاد',
    lieu_naissance VARCHAR(100) COMMENT 'مكان الميلاد',
    lieu_naissance_arabe VARCHAR(100) COMMENT 'مكان الميلاد بالعربية',
    
    -- معلومات طبية
    groupe_sanguin VARCHAR(5) COMMENT 'فصيلة الدم',
    
    -- معلومات مهنية
    structure VARCHAR(200) COMMENT 'الهيكل/المؤسسة',
    type_employe VARCHAR(50) COMMENT 'نوع الموظف',
    corps VARCHAR(100) COMMENT 'السلك',
    grade VARCHAR(100) COMMENT 'الرتبة',
    filiere VARCHAR(100) COMMENT 'الفرع',
    echelon VARCHAR(20) COMMENT 'الدرجة',
    categorie VARCHAR(50) COMMENT 'الفئة',
    position_actuelle VARCHAR(100) COMMENT 'المنصب الحالي',
    
    -- تواريخ مهمة
    date_recrutement DATE COMMENT 'تاريخ التوظيف',
    date_installation DATE COMMENT 'تاريخ التنصيب',
    
    -- معلومات مالية
    numero_securite_sociale VARCHAR(20) COMMENT 'رقم الضمان الاجتماعي',
    numero_compte VARCHAR(30) COMMENT 'رقم الحساب',
    date_affiliation DATE COMMENT 'تاريخ الانتساب',
    
    -- معلومات تقنية
    carte_rfid VARCHAR(20) COMMENT 'بطاقة RFID',
    photo_path VARCHAR(500) COMMENT 'مسار الصورة الشخصية',
    
    -- معلومات النظام
    status ENUM('active', 'inactive', 'retired') DEFAULT 'active' COMMENT 'حالة الموظف',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    created_by INT COMMENT 'المستخدم الذي أضاف السجل',
    
    -- الفهارس
    INDEX idx_nom (nom),
    INDEX idx_prenom (prenom),
    INDEX idx_matricule (matricule),
    INDEX idx_structure (structure),
    INDEX idx_grade (grade),
    INDEX idx_corps (corps),
    INDEX idx_status (status),
    INDEX idx_date_recrutement (date_recrutement),
    INDEX idx_employee_search (nom, prenom, matricule),
    INDEX idx_employee_professional (structure, grade, corps),
    
    -- المراجع الخارجية
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- فهرس نصي للبحث الشامل
    FULLTEXT KEY ft_names (nom, prenom, nom_arabe, prenom_arabe)
) ENGINE=InnoDB COMMENT='جدول الموظفين';

-- 3. جدول أنواع الوثائق
CREATE TABLE document_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'اسم نوع الوثيقة',
    type_name_arabe VARCHAR(100) COMMENT 'اسم نوع الوثيقة بالعربية',
    description TEXT COMMENT 'وصف نوع الوثيقة',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'هل الوثيقة مطلوبة',
    category VARCHAR(50) COMMENT 'فئة الوثيقة',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'هل النوع نشط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB COMMENT='جدول أنواع الوثائق';

-- 4. جدول الوثائق
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL COMMENT 'معرف الموظف',
    document_type_id INT NOT NULL COMMENT 'معرف نوع الوثيقة',
    
    -- معلومات الوثيقة
    document_title VARCHAR(200) COMMENT 'عنوان الوثيقة',
    document_number VARCHAR(50) COMMENT 'رقم الوثيقة',
    issue_date DATE COMMENT 'تاريخ الإصدار',
    expiry_date DATE COMMENT 'تاريخ انتهاء الصلاحية',
    issuing_authority VARCHAR(100) COMMENT 'جهة الإصدار',
    
    -- معلومات الملف
    file_name VARCHAR(255) NOT NULL COMMENT 'اسم الملف الأصلي',
    file_path VARCHAR(500) NOT NULL COMMENT 'مسار الملف',
    file_size INT COMMENT 'حجم الملف بالبايت',
    file_type VARCHAR(10) COMMENT 'نوع الملف (PDF, JPG, PNG)',
    file_hash VARCHAR(64) COMMENT 'hash للملف للتحقق من التكامل',
    
    -- معلومات إضافية
    notes TEXT COMMENT 'ملاحظات',
    is_verified BOOLEAN DEFAULT FALSE COMMENT 'هل تم التحقق من الوثيقة',
    verification_date DATE COMMENT 'تاريخ التحقق',
    verified_by INT COMMENT 'المستخدم الذي تحقق من الوثيقة',
    
    -- معلومات النظام
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active' COMMENT 'حالة الوثيقة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    uploaded_by INT NOT NULL COMMENT 'المستخدم الذي رفع الوثيقة',
    
    -- المراجع الخارجية
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- الفهارس
    INDEX idx_employee_id (employee_id),
    INDEX idx_document_type (document_type_id),
    INDEX idx_issue_date (issue_date),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_document_search (employee_id, document_type_id, status),
    INDEX idx_document_dates (issue_date, expiry_date),
    
    -- فهرس نصي للبحث
    FULLTEXT KEY ft_document_content (document_title, notes)
) ENGINE=InnoDB COMMENT='جدول الوثائق';

-- 5. جدول سجل العمليات
CREATE TABLE audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT 'معرف المستخدم',
    action VARCHAR(50) NOT NULL COMMENT 'نوع العملية',
    table_name VARCHAR(50) COMMENT 'اسم الجدول المتأثر',
    record_id INT COMMENT 'معرف السجل المتأثر',
    old_values JSON COMMENT 'القيم القديمة',
    new_values JSON COMMENT 'القيم الجديدة',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ العملية',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at),
    INDEX idx_record_lookup (table_name, record_id)
) ENGINE=InnoDB COMMENT='سجل العمليات';

-- 6. جدول الجلسات
CREATE TABLE sessions (
    id VARCHAR(128) PRIMARY KEY COMMENT 'معرف الجلسة',
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    payload TEXT NOT NULL COMMENT 'بيانات الجلسة',
    last_activity INT NOT NULL COMMENT 'آخر نشاط',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB COMMENT='جدول الجلسات';

-- 7. جدول النسخ الاحتياطية للوثائق
CREATE TABLE document_backups (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف النسخة الاحتياطية',
    original_document_id INT NOT NULL COMMENT 'معرف الوثيقة الأصلية',
    backup_filename VARCHAR(255) NOT NULL COMMENT 'اسم ملف النسخة الاحتياطية',
    backup_path VARCHAR(500) NOT NULL COMMENT 'مسار النسخة الاحتياطية',
    backup_size BIGINT NOT NULL COMMENT 'حجم النسخة الاحتياطية بالبايت',
    backup_hash VARCHAR(64) NOT NULL COMMENT 'هاش النسخة الاحتياطية',
    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء النسخة الاحتياطية',
    created_by INT NOT NULL COMMENT 'منشئ النسخة الاحتياطية',
    backup_type ENUM('pre_modification', 'pre_deletion', 'scheduled', 'manual') DEFAULT 'manual' COMMENT 'نوع النسخة الاحتياطية',
    original_metadata JSON COMMENT 'البيانات الوصفية الأصلية',
    backup_notes TEXT COMMENT 'ملاحظات النسخة الاحتياطية',
    retention_period INT DEFAULT 365 COMMENT 'فترة الاحتفاظ بالأيام',
    is_compressed BOOLEAN DEFAULT FALSE COMMENT 'هل النسخة مضغوطة',
    compression_ratio DECIMAL(5,2) COMMENT 'نسبة الضغط',

    FOREIGN KEY (original_document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_backup_document (original_document_id),
    INDEX idx_backup_date (backup_date),
    INDEX idx_backup_type (backup_type),
    INDEX idx_backup_hash (backup_hash),
    INDEX idx_backup_created_by (created_by)
) ENGINE=InnoDB COMMENT='النسخ الاحتياطية للوثائق';

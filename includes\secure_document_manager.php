<?php
/**
 * مدير الوثائق الآمن - حفظ ومسح الوثائق
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'auth.php';

class SecureDocumentManager {
    private $db;
    private $user_id;
    private $user_role;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->user_id = $_SESSION['user_id'] ?? null;
        $this->user_role = $_SESSION['user_role'] ?? null;
        
        if (!$this->user_id) {
            throw new Exception('المستخدم غير مسجل الدخول');
        }
    }
    
    /**
     * حفظ الوثيقة مع النسخ الاحتياطية
     */
    public function saveDocument($document_id, $data, $options = []) {
        try {
            // التحقق من الصلاحيات
            if (!$this->canModifyDocument($document_id)) {
                throw new Exception('غير مصرح لك بتعديل هذه الوثيقة');
            }
            
            // جلب تفاصيل الوثيقة
            $document = $this->getDocumentDetails($document_id);
            if (!$document) {
                throw new Exception('الوثيقة غير موجودة');
            }
            
            // بدء المعاملة
            $this->db->beginTransaction();
            
            try {
                // إنشاء نسخة احتياطية
                $backup_info = $this->createBackup($document);
                
                // حفظ التغييرات
                $save_result = $this->performSave($document_id, $data, $options);
                
                // تسجيل العملية
                $this->logOperation('document_saved', $document_id, [
                    'backup_id' => $backup_info['backup_id'],
                    'changes' => $data,
                    'options' => $options
                ]);
                
                // تأكيد المعاملة
                $this->db->commit();
                
                return [
                    'success' => true,
                    'message' => 'تم حفظ الوثيقة بنجاح',
                    'backup_info' => $backup_info,
                    'save_result' => $save_result
                ];
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * حذف الوثيقة مع تأكيدات الأمان
     */
    public function deleteDocument($document_id, $options = []) {
        try {
            // التحقق من الصلاحيات
            if (!$this->canDeleteDocument($document_id)) {
                throw new Exception('غير مصرح لك بحذف هذه الوثيقة');
            }
            
            // جلب تفاصيل الوثيقة
            $document = $this->getDocumentDetails($document_id);
            if (!$document) {
                throw new Exception('الوثيقة غير موجودة');
            }
            
            // التحقق من قيود الحذف
            $this->validateDeletion($document, $options);
            
            // بدء المعاملة
            $this->db->beginTransaction();
            
            try {
                // إنشاء نسخة احتياطية قبل الحذف
                $backup_info = $this->createDeletionBackup($document);
                
                // تنفيذ الحذف
                $deletion_result = $this->performDeletion($document, $options);
                
                // تسجيل العملية
                $this->logOperation('document_deleted', $document_id, [
                    'backup_info' => $backup_info,
                    'deletion_type' => $options['deletion_type'] ?? 'soft',
                    'reason' => $options['reason'] ?? 'user_request'
                ]);
                
                // تأكيد المعاملة
                $this->db->commit();
                
                return [
                    'success' => true,
                    'message' => 'تم حذف الوثيقة بنجاح',
                    'backup_info' => $backup_info,
                    'can_restore' => true
                ];
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * استعادة الوثيقة من النسخة الاحتياطية
     */
    public function restoreDocument($backup_id, $options = []) {
        try {
            // التحقق من الصلاحيات
            if (!$this->canRestoreDocument($backup_id)) {
                throw new Exception('غير مصرح لك باستعادة هذه الوثيقة');
            }
            
            // جلب معلومات النسخة الاحتياطية
            $backup = $this->getBackupDetails($backup_id);
            if (!$backup) {
                throw new Exception('النسخة الاحتياطية غير موجودة');
            }
            
            // بدء المعاملة
            $this->db->beginTransaction();
            
            try {
                // تنفيذ الاستعادة
                $restore_result = $this->performRestore($backup, $options);
                
                // تسجيل العملية
                $this->logOperation('document_restored', $backup['original_document_id'], [
                    'backup_id' => $backup_id,
                    'restore_options' => $options
                ]);
                
                // تأكيد المعاملة
                $this->db->commit();
                
                return [
                    'success' => true,
                    'message' => 'تم استعادة الوثيقة بنجاح',
                    'restored_document_id' => $restore_result['document_id']
                ];
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية
     */
    private function createBackup($document) {
        $backup_folder = UPLOAD_PATH . '/backups/' . date('Y/m');
        if (!is_dir($backup_folder)) {
            mkdir($backup_folder, 0755, true);
        }
        
        $original_file = UPLOAD_PATH . $document['file_path'];
        $backup_filename = 'backup_' . $document['id'] . '_' . date('Y-m-d_H-i-s') . '_' . $document['filename'];
        $backup_file_path = $backup_folder . '/' . $backup_filename;
        
        // نسخ الملف
        if (file_exists($original_file)) {
            if (!copy($original_file, $backup_file_path)) {
                throw new Exception('فشل في إنشاء النسخة الاحتياطية');
            }
        }
        
        // حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
        $backup_data = [
            'original_document_id' => $document['id'],
            'backup_filename' => $backup_filename,
            'backup_path' => str_replace(UPLOAD_PATH, '', $backup_file_path),
            'backup_size' => filesize($backup_file_path),
            'backup_hash' => hash_file('sha256', $backup_file_path),
            'backup_date' => date('Y-m-d H:i:s'),
            'created_by' => $this->user_id,
            'backup_type' => 'pre_modification',
            'original_metadata' => $document['metadata']
        ];
        
        $backup_id = $this->db->insert('document_backups', $backup_data);
        
        return [
            'backup_id' => $backup_id,
            'backup_path' => $backup_file_path,
            'backup_filename' => $backup_filename
        ];
    }
    
    /**
     * إنشاء نسخة احتياطية قبل الحذف
     */
    private function createDeletionBackup($document) {
        $backup_info = $this->createBackup($document);
        
        // تحديث نوع النسخة الاحتياطية
        $this->db->update('document_backups', 
            ['backup_type' => 'pre_deletion'], 
            ['id' => $backup_info['backup_id']]
        );
        
        return $backup_info;
    }
    
    /**
     * تنفيذ عملية الحفظ
     */
    private function performSave($document_id, $data, $options) {
        // تحديث بيانات الوثيقة
        $update_data = [];
        
        if (isset($data['filename'])) {
            $update_data['filename'] = $data['filename'];
        }
        
        if (isset($data['file_content'])) {
            // حفظ المحتوى الجديد
            $document = $this->getDocumentDetails($document_id);
            $file_path = UPLOAD_PATH . $document['file_path'];
            
            if (!file_put_contents($file_path, $data['file_content'])) {
                throw new Exception('فشل في حفظ محتوى الملف');
            }
            
            $update_data['file_size'] = filesize($file_path);
            $update_data['file_hash'] = hash_file('sha256', $file_path);
        }
        
        if (isset($data['metadata'])) {
            $existing_metadata = json_decode($document['metadata'], true) ?: [];
            $new_metadata = array_merge($existing_metadata, $data['metadata']);
            $update_data['metadata'] = json_encode($new_metadata, JSON_UNESCAPED_UNICODE);
        }
        
        // إضافة معلومات التحديث
        $update_data['last_modified'] = date('Y-m-d H:i:s');
        $update_data['modified_by'] = $this->user_id;
        
        // تنفيذ التحديث
        $updated = $this->db->update('documents', $update_data, ['id' => $document_id]);
        
        if (!$updated) {
            throw new Exception('فشل في حفظ التغييرات');
        }
        
        return [
            'updated_fields' => array_keys($update_data),
            'update_time' => $update_data['last_modified']
        ];
    }
    
    /**
     * تنفيذ عملية الحذف
     */
    private function performDeletion($document, $options) {
        $deletion_type = $options['deletion_type'] ?? 'soft';
        
        if ($deletion_type === 'soft') {
            // حذف ناعم - تحديث الحالة فقط
            $update_data = [
                'status' => 'deleted',
                'deleted_at' => date('Y-m-d H:i:s'),
                'deleted_by' => $this->user_id,
                'deletion_reason' => $options['reason'] ?? 'user_request'
            ];
            
            $this->db->update('documents', $update_data, ['id' => $document['id']]);
            
        } else {
            // حذف صلب - حذف السجل والملف
            
            // حذف الملف الفعلي
            $file_path = UPLOAD_PATH . $document['file_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            
            // حذف الصور المصغرة
            $thumbnail_path = dirname($file_path) . '/thumbnails/thumb_' . basename($file_path);
            if (file_exists($thumbnail_path)) {
                unlink($thumbnail_path);
            }
            
            // حذف السجل من قاعدة البيانات
            $this->db->delete('documents', ['id' => $document['id']]);
        }
        
        return [
            'deletion_type' => $deletion_type,
            'deletion_time' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * تنفيذ عملية الاستعادة
     */
    private function performRestore($backup, $options) {
        $restore_type = $options['restore_type'] ?? 'replace';
        
        if ($restore_type === 'replace') {
            // استبدال الوثيقة الحالية
            $backup_file = UPLOAD_PATH . $backup['backup_path'];
            $original_document = $this->getDocumentDetails($backup['original_document_id']);
            
            if ($original_document) {
                $target_file = UPLOAD_PATH . $original_document['file_path'];
                
                if (!copy($backup_file, $target_file)) {
                    throw new Exception('فشل في استعادة الملف');
                }
                
                // تحديث معلومات الوثيقة
                $update_data = [
                    'file_size' => filesize($target_file),
                    'file_hash' => hash_file('sha256', $target_file),
                    'status' => 'active',
                    'restored_at' => date('Y-m-d H:i:s'),
                    'restored_by' => $this->user_id,
                    'restored_from_backup' => $backup['id']
                ];
                
                $this->db->update('documents', $update_data, ['id' => $backup['original_document_id']]);
                
                return ['document_id' => $backup['original_document_id']];
            }
        }
        
        // إنشاء وثيقة جديدة من النسخة الاحتياطية
        $backup_file = UPLOAD_PATH . $backup['backup_path'];
        $new_filename = 'restored_' . date('Y-m-d_H-i-s') . '_' . $backup['backup_filename'];
        
        // نسخ الملف إلى موقع جديد
        $employee_folder = UPLOAD_PATH . '/documents/' . $original_document['employee_id'];
        $new_file_path = $employee_folder . '/' . $new_filename;
        
        if (!copy($backup_file, $new_file_path)) {
            throw new Exception('فشل في إنشاء الوثيقة المستعادة');
        }
        
        // إنشاء سجل جديد
        $new_document_data = [
            'employee_id' => $original_document['employee_id'],
            'document_type_id' => $original_document['document_type_id'],
            'filename' => $new_filename,
            'original_filename' => $original_document['original_filename'] . ' (مستعادة)',
            'file_path' => str_replace(UPLOAD_PATH, '', $new_file_path),
            'file_size' => filesize($new_file_path),
            'file_type' => $original_document['file_type'],
            'file_hash' => hash_file('sha256', $new_file_path),
            'upload_date' => date('Y-m-d H:i:s'),
            'uploaded_by' => $this->user_id,
            'status' => 'active',
            'is_restored' => 1,
            'restored_from_backup' => $backup['id'],
            'metadata' => $backup['original_metadata']
        ];
        
        $new_document_id = $this->db->insert('documents', $new_document_data);
        
        return ['document_id' => $new_document_id];
    }
    
    /**
     * التحقق من صلاحية تعديل الوثيقة
     */
    private function canModifyDocument($document_id) {
        if (hasRole('admin') || hasRole('hr_manager')) {
            return true;
        }
        
        $document = $this->getDocumentDetails($document_id);
        if (!$document) {
            return false;
        }
        
        // المستخدم يمكنه تعديل الوثائق التي رفعها
        return $document['uploaded_by'] == $this->user_id;
    }
    
    /**
     * التحقق من صلاحية حذف الوثيقة
     */
    private function canDeleteDocument($document_id) {
        if (hasRole('admin')) {
            return true;
        }
        
        if (hasRole('hr_manager')) {
            return true;
        }
        
        $document = $this->getDocumentDetails($document_id);
        if (!$document) {
            return false;
        }
        
        // المستخدم يمكنه حذف الوثائق التي رفعها
        return $document['uploaded_by'] == $this->user_id;
    }
    
    /**
     * التحقق من صلاحية استعادة الوثيقة
     */
    private function canRestoreDocument($backup_id) {
        if (hasRole('admin') || hasRole('hr_manager')) {
            return true;
        }
        
        $backup = $this->getBackupDetails($backup_id);
        if (!$backup) {
            return false;
        }
        
        // المستخدم يمكنه استعادة النسخ الاحتياطية التي أنشأها
        return $backup['created_by'] == $this->user_id;
    }
    
    /**
     * جلب تفاصيل الوثيقة
     */
    private function getDocumentDetails($document_id) {
        return $this->db->fetchOne("
            SELECT d.*, e.id as employee_id
            FROM documents d
            JOIN employees e ON d.employee_id = e.id
            WHERE d.id = ?
        ", [$document_id]);
    }
    
    /**
     * جلب تفاصيل النسخة الاحتياطية
     */
    private function getBackupDetails($backup_id) {
        return $this->db->fetchOne("
            SELECT * FROM document_backups WHERE id = ?
        ", [$backup_id]);
    }
    
    /**
     * التحقق من قيود الحذف
     */
    private function validateDeletion($document, $options) {
        // التحقق من الوثائق المرتبطة
        $related_docs = $this->db->fetchAll("
            SELECT id FROM documents 
            WHERE parent_document_id = ? AND status != 'deleted'
        ", [$document['id']]);
        
        if (!empty($related_docs) && !isset($options['force_delete'])) {
            throw new Exception('لا يمكن حذف هذه الوثيقة لوجود وثائق مرتبطة بها');
        }
        
        // التحقق من الوثائق المطلوبة
        if ($document['is_required'] && !hasRole('admin')) {
            throw new Exception('لا يمكن حذف الوثائق المطلوبة');
        }
    }
    
    /**
     * تسجيل العملية
     */
    private function logOperation($operation, $document_id, $details) {
        logActivity($operation, 'documents', $document_id, $details);
    }
}

// معالج الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من تسجيل الدخول
        if (!isLoggedIn()) {
            throw new Exception('يجب تسجيل الدخول أولاً');
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        $manager = new SecureDocumentManager();
        
        switch ($action) {
            case 'save':
                $result = $manager->saveDocument(
                    $input['document_id'],
                    $input['data'],
                    $input['options'] ?? []
                );
                break;
                
            case 'delete':
                $result = $manager->deleteDocument(
                    $input['document_id'],
                    $input['options'] ?? []
                );
                break;
                
            case 'restore':
                $result = $manager->restoreDocument(
                    $input['backup_id'],
                    $input['options'] ?? []
                );
                break;
                
            default:
                throw new Exception('عملية غير صحيحة');
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}
?>

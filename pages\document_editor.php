<?php
/**
 * محرر الوثائق المتقدم
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('edit_document')) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$page_title = 'محرر الوثائق';

// الحصول على معرف الوثيقة
$document_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($document_id <= 0) {
    header('Location: dashboard.php');
    exit;
}

// جلب تفاصيل الوثيقة
$document = $db->fetchOne("
    SELECT d.*, 
           dt.type_name_arabe, dt.category,
           e.nom, e.prenom, e.matricule, e.structure
    FROM documents d
    JOIN document_types dt ON d.document_type_id = dt.id
    JOIN employees e ON d.employee_id = e.id
    WHERE d.id = ?
", [$document_id]);

if (!$document) {
    header('Location: dashboard.php');
    exit;
}

// التحقق من أن الوثيقة قابلة للتحرير (صورة)
$is_editable = in_array($document['file_type'], ['image/jpeg', 'image/jpg', 'image/png']);

if (!$is_editable) {
    header('Location: document_scanner.php?employee_id=' . $document['employee_id']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام إدارة الوثائق</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Fabric.js for image editing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .editor-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .toolbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: white;
        }
        
        .tool-group {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.5rem;
            margin: 0.25rem;
            display: inline-block;
        }
        
        .tool-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 5px;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tool-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .tool-btn.active {
            background: rgba(255, 255, 255, 0.4);
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .canvas-container {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .properties-panel {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .slider-container {
            margin: 1rem 0;
        }
        
        .slider {
            width: 100%;
            margin: 0.5rem 0;
        }
        
        .color-picker {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .history-panel {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .history-item {
            padding: 0.5rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .history-item:hover {
            background: #f8f9fa;
        }
        
        .save-options {
            background: linear-gradient(45deg, #28a745, #20c997);
            border-radius: 10px;
            padding: 1rem;
            color: white;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-image"></i> محرر الوثائق
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <strong><?php echo htmlspecialchars($document['nom'] . ' ' . $document['prenom']); ?></strong>
                        - <?php echo htmlspecialchars($document['type_name_arabe']); ?>
                    </span>
                    <a class="nav-link" href="document_scanner.php?employee_id=<?php echo $document['employee_id']; ?>">
                        <i class="bi bi-arrow-left"></i> العودة
                    </a>
                </div>
            </div>
        </nav>
        
        <div class="row">
            <!-- شريط الأدوات -->
            <div class="col-12">
                <div class="toolbar">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- أدوات التحديد والتحرير -->
                            <div class="tool-group">
                                <label class="text-white-50 small">التحديد:</label><br>
                                <button class="tool-btn active" id="selectTool" onclick="setTool('select')">
                                    <i class="bi bi-cursor"></i> تحديد
                                </button>
                                <button class="tool-btn" id="cropTool" onclick="setTool('crop')">
                                    <i class="bi bi-crop"></i> قص
                                </button>
                                <button class="tool-btn" id="drawTool" onclick="setTool('draw')">
                                    <i class="bi bi-pencil"></i> رسم
                                </button>
                                <button class="tool-btn" id="textTool" onclick="setTool('text')">
                                    <i class="bi bi-type"></i> نص
                                </button>
                            </div>
                            
                            <!-- أدوات التحويل -->
                            <div class="tool-group">
                                <label class="text-white-50 small">التحويل:</label><br>
                                <button class="tool-btn" onclick="rotateImage(90)">
                                    <i class="bi bi-arrow-clockwise"></i> دوران يمين
                                </button>
                                <button class="tool-btn" onclick="rotateImage(-90)">
                                    <i class="bi bi-arrow-counterclockwise"></i> دوران يسار
                                </button>
                                <button class="tool-btn" onclick="flipHorizontal()">
                                    <i class="bi bi-arrows-expand"></i> انعكاس أفقي
                                </button>
                                <button class="tool-btn" onclick="flipVertical()">
                                    <i class="bi bi-arrows-collapse"></i> انعكاس عمودي
                                </button>
                            </div>
                            
                            <!-- أدوات التحسين -->
                            <div class="tool-group">
                                <label class="text-white-50 small">التحسين:</label><br>
                                <button class="tool-btn" onclick="autoEnhance()">
                                    <i class="bi bi-magic"></i> تحسين تلقائي
                                </button>
                                <button class="tool-btn" onclick="removeBackground()">
                                    <i class="bi bi-eraser"></i> إزالة الخلفية
                                </button>
                                <button class="tool-btn" onclick="straightenDocument()">
                                    <i class="bi bi-align-center"></i> تقويم الوثيقة
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-4 text-end">
                            <!-- أدوات التراجع والحفظ -->
                            <div class="tool-group">
                                <button class="tool-btn" onclick="undoAction()" id="undoBtn">
                                    <i class="bi bi-arrow-counterclockwise"></i> تراجع
                                </button>
                                <button class="tool-btn" onclick="redoAction()" id="redoBtn">
                                    <i class="bi bi-arrow-clockwise"></i> إعادة
                                </button>
                                <button class="tool-btn" onclick="resetImage()">
                                    <i class="bi bi-arrow-repeat"></i> إعادة تعيين
                                </button>
                            </div>
                            
                            <div class="tool-group">
                                <button class="tool-btn" onclick="saveDocument()">
                                    <i class="bi bi-save"></i> حفظ
                                </button>
                                <button class="tool-btn" onclick="exportDocument()">
                                    <i class="bi bi-download"></i> تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- منطقة التحرير -->
            <div class="col-md-9">
                <div class="editor-container">
                    <div class="canvas-container" id="canvasContainer">
                        <canvas id="imageCanvas"></canvas>
                    </div>
                    
                    <!-- شريط الحالة -->
                    <div class="mt-2 d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <span id="imageInfo">جاري التحميل...</span>
                            </small>
                        </div>
                        <div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" onclick="zoomOut()">
                                    <i class="bi bi-zoom-out"></i>
                                </button>
                                <span class="btn btn-outline-secondary" id="zoomLevel">100%</span>
                                <button class="btn btn-outline-secondary" onclick="zoomIn()">
                                    <i class="bi bi-zoom-in"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="fitToScreen()">
                                    <i class="bi bi-arrows-fullscreen"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- لوحة الخصائص -->
            <div class="col-md-3">
                <div class="properties-panel">
                    <h6><i class="bi bi-sliders"></i> خصائص التحرير</h6>
                    
                    <!-- تحكم في السطوع والتباين -->
                    <div class="slider-container">
                        <label class="form-label small">السطوع:</label>
                        <input type="range" class="slider" id="brightnessSlider" 
                               min="-100" max="100" value="0" 
                               oninput="adjustBrightness(this.value)">
                        <span id="brightnessValue">0</span>
                    </div>
                    
                    <div class="slider-container">
                        <label class="form-label small">التباين:</label>
                        <input type="range" class="slider" id="contrastSlider" 
                               min="-100" max="100" value="0" 
                               oninput="adjustContrast(this.value)">
                        <span id="contrastValue">0</span>
                    </div>
                    
                    <div class="slider-container">
                        <label class="form-label small">التشبع:</label>
                        <input type="range" class="slider" id="saturationSlider" 
                               min="-100" max="100" value="0" 
                               oninput="adjustSaturation(this.value)">
                        <span id="saturationValue">0</span>
                    </div>
                    
                    <!-- أدوات الرسم -->
                    <div id="drawingTools" style="display: none;">
                        <hr>
                        <h6><i class="bi bi-palette"></i> أدوات الرسم</h6>
                        
                        <div class="mb-2">
                            <label class="form-label small">لون القلم:</label>
                            <input type="color" class="color-picker" id="penColor" value="#ff0000">
                        </div>
                        
                        <div class="slider-container">
                            <label class="form-label small">سمك القلم:</label>
                            <input type="range" class="slider" id="penWidth" 
                                   min="1" max="20" value="3" 
                                   oninput="setPenWidth(this.value)">
                            <span id="penWidthValue">3</span>
                        </div>
                    </div>
                    
                    <!-- أدوات النص -->
                    <div id="textTools" style="display: none;">
                        <hr>
                        <h6><i class="bi bi-type"></i> أدوات النص</h6>
                        
                        <div class="mb-2">
                            <label class="form-label small">لون النص:</label>
                            <input type="color" class="color-picker" id="textColor" value="#000000">
                        </div>
                        
                        <div class="mb-2">
                            <label class="form-label small">حجم الخط:</label>
                            <select class="form-select form-select-sm" id="fontSize">
                                <option value="12">12</option>
                                <option value="16">16</option>
                                <option value="20" selected>20</option>
                                <option value="24">24</option>
                                <option value="32">32</option>
                                <option value="48">48</option>
                            </select>
                        </div>
                        
                        <div class="mb-2">
                            <label class="form-label small">نوع الخط:</label>
                            <select class="form-select form-select-sm" id="fontFamily">
                                <option value="Arial">Arial</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Courier New">Courier New</option>
                                <option value="Tahoma">Tahoma</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- سجل التغييرات -->
                    <hr>
                    <h6><i class="bi bi-clock-history"></i> سجل التغييرات</h6>
                    <div class="history-panel" id="historyPanel">
                        <div class="history-item">تحميل الصورة الأصلية</div>
                    </div>
                </div>
                
                <!-- خيارات الحفظ -->
                <div class="save-options">
                    <h6><i class="bi bi-save"></i> خيارات الحفظ</h6>
                    
                    <div class="mb-2">
                        <label class="form-label small">جودة الصورة:</label>
                        <select class="form-select form-select-sm" id="saveQuality">
                            <option value="0.6">منخفضة (60%)</option>
                            <option value="0.8" selected>متوسطة (80%)</option>
                            <option value="0.9">عالية (90%)</option>
                            <option value="1.0">أقصى جودة (100%)</option>
                        </select>
                    </div>
                    
                    <div class="mb-2">
                        <label class="form-label small">تنسيق الحفظ:</label>
                        <select class="form-select form-select-sm" id="saveFormat">
                            <option value="jpeg">JPEG</option>
                            <option value="png">PNG</option>
                            <option value="pdf">PDF</option>
                        </select>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="keepOriginal" checked>
                        <label class="form-check-label small" for="keepOriginal">
                            الاحتفاظ بالنسخة الأصلية
                        </label>
                    </div>
                    
                    <button class="btn btn-light w-100" onclick="saveDocument()">
                        <i class="bi bi-save"></i> حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري المعالجة...</span>
                    </div>
                    <p class="mt-2 mb-0">جاري معالجة الصورة...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // متغيرات عامة
        let canvas;
        let originalImageData;
        let currentTool = 'select';
        let history = [];
        let historyStep = 0;
        let isDrawing = false;
        let currentZoom = 1;
        
        // بيانات الوثيقة
        const documentData = {
            id: <?php echo $document_id; ?>,
            filename: '<?php echo addslashes($document['filename']); ?>',
            filePath: '<?php echo addslashes($document['file_path']); ?>',
            employeeId: <?php echo $document['employee_id']; ?>
        };
        
        // تهيئة المحرر
        document.addEventListener('DOMContentLoaded', function() {
            initializeEditor();
            loadDocument();
        });
        
        // تهيئة محرر الصور
        function initializeEditor() {
            canvas = new fabric.Canvas('imageCanvas', {
                width: 800,
                height: 600,
                backgroundColor: '#ffffff'
            });
            
            // إعداد أحداث الماوس للرسم
            canvas.on('mouse:down', function(e) {
                if (currentTool === 'draw') {
                    isDrawing = true;
                    const pointer = canvas.getPointer(e.e);
                    const points = [pointer.x, pointer.y, pointer.x, pointer.y];
                    
                    const line = new fabric.Path(`M ${points[0]} ${points[1]} L ${points[2]} ${points[3]}`, {
                        stroke: document.getElementById('penColor').value,
                        strokeWidth: parseInt(document.getElementById('penWidth').value),
                        fill: '',
                        selectable: false
                    });
                    
                    canvas.add(line);
                    canvas.renderAll();
                }
            });
            
            canvas.on('mouse:move', function(e) {
                if (isDrawing && currentTool === 'draw') {
                    const pointer = canvas.getPointer(e.e);
                    const objects = canvas.getObjects();
                    const lastObject = objects[objects.length - 1];
                    
                    if (lastObject && lastObject.type === 'path') {
                        const path = lastObject.path;
                        path.push(['L', pointer.x, pointer.y]);
                        lastObject.path = path;
                        canvas.renderAll();
                    }
                }
            });
            
            canvas.on('mouse:up', function(e) {
                if (currentTool === 'draw') {
                    isDrawing = false;
                    saveToHistory('رسم خط');
                }
            });
            
            // إعداد أحداث النص
            canvas.on('mouse:dblclick', function(e) {
                if (currentTool === 'text') {
                    const pointer = canvas.getPointer(e.e);
                    const text = prompt('أدخل النص:');
                    
                    if (text) {
                        const textObject = new fabric.Text(text, {
                            left: pointer.x,
                            top: pointer.y,
                            fill: document.getElementById('textColor').value,
                            fontSize: parseInt(document.getElementById('fontSize').value),
                            fontFamily: document.getElementById('fontFamily').value
                        });
                        
                        canvas.add(textObject);
                        canvas.renderAll();
                        saveToHistory('إضافة نص');
                    }
                }
            });
        }
        
        // تحميل الوثيقة
        function loadDocument() {
            showLoading(true);
            
            const imagePath = '../uploads' + documentData.filePath;
            
            fabric.Image.fromURL(imagePath, function(img) {
                // حفظ البيانات الأصلية
                originalImageData = img;
                
                // تحديد حجم الكانفاس بناءً على الصورة
                const maxWidth = 800;
                const maxHeight = 600;
                
                let scale = 1;
                if (img.width > maxWidth || img.height > maxHeight) {
                    scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                }
                
                canvas.setWidth(img.width * scale);
                canvas.setHeight(img.height * scale);
                
                img.scale(scale);
                img.set({
                    left: 0,
                    top: 0,
                    selectable: false
                });
                
                canvas.clear();
                canvas.add(img);
                canvas.renderAll();
                
                // تحديث معلومات الصورة
                updateImageInfo(img.width, img.height);
                
                // حفظ الحالة الأولى
                saveToHistory('تحميل الصورة الأصلية');
                
                showLoading(false);
            }, {
                crossOrigin: 'anonymous'
            });
        }
        
        // تحديد الأداة الحالية
        function setTool(tool) {
            currentTool = tool;
            
            // تحديث أزرار الأدوات
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(tool + 'Tool').classList.add('active');
            
            // إظهار/إخفاء لوحات الأدوات
            document.getElementById('drawingTools').style.display = tool === 'draw' ? 'block' : 'none';
            document.getElementById('textTools').style.display = tool === 'text' ? 'block' : 'none';
            
            // تحديد وضع التحديد
            canvas.selection = tool === 'select';
            canvas.forEachObject(function(obj) {
                obj.selectable = tool === 'select';
            });
            
            canvas.renderAll();
        }
        
        // دوران الصورة
        function rotateImage(angle) {
            const objects = canvas.getObjects();
            const mainImage = objects[0]; // الصورة الأساسية
            
            if (mainImage) {
                mainImage.rotate(mainImage.angle + angle);
                canvas.renderAll();
                saveToHistory(`دوران ${angle} درجة`);
            }
        }
        
        // انعكاس أفقي
        function flipHorizontal() {
            const objects = canvas.getObjects();
            const mainImage = objects[0];
            
            if (mainImage) {
                mainImage.set('flipX', !mainImage.flipX);
                canvas.renderAll();
                saveToHistory('انعكاس أفقي');
            }
        }
        
        // انعكاس عمودي
        function flipVertical() {
            const objects = canvas.getObjects();
            const mainImage = objects[0];
            
            if (mainImage) {
                mainImage.set('flipY', !mainImage.flipY);
                canvas.renderAll();
                saveToHistory('انعكاس عمودي');
            }
        }
        
        // تحسين تلقائي
        function autoEnhance() {
            showLoading(true);
            
            // محاكاة تحسين تلقائي
            setTimeout(() => {
                adjustBrightness(10);
                adjustContrast(15);
                saveToHistory('تحسين تلقائي');
                showLoading(false);
            }, 1000);
        }
        
        // تعديل السطوع
        function adjustBrightness(value) {
            const objects = canvas.getObjects();
            const mainImage = objects[0];
            
            if (mainImage) {
                const filter = new fabric.Image.filters.Brightness({
                    brightness: value / 100
                });
                
                mainImage.filters = mainImage.filters || [];
                mainImage.filters[0] = filter;
                mainImage.applyFilters();
                canvas.renderAll();
                
                document.getElementById('brightnessValue').textContent = value;
            }
        }
        
        // تعديل التباين
        function adjustContrast(value) {
            const objects = canvas.getObjects();
            const mainImage = objects[0];
            
            if (mainImage) {
                const filter = new fabric.Image.filters.Contrast({
                    contrast: value / 100
                });
                
                mainImage.filters = mainImage.filters || [];
                mainImage.filters[1] = filter;
                mainImage.applyFilters();
                canvas.renderAll();
                
                document.getElementById('contrastValue').textContent = value;
            }
        }
        
        // تعديل التشبع
        function adjustSaturation(value) {
            const objects = canvas.getObjects();
            const mainImage = objects[0];
            
            if (mainImage) {
                const filter = new fabric.Image.filters.Saturation({
                    saturation: value / 100
                });
                
                mainImage.filters = mainImage.filters || [];
                mainImage.filters[2] = filter;
                mainImage.applyFilters();
                canvas.renderAll();
                
                document.getElementById('saturationValue').textContent = value;
            }
        }
        
        // حفظ في السجل
        function saveToHistory(action) {
            const state = JSON.stringify(canvas.toJSON());
            
            // إزالة الحالات اللاحقة إذا كنا في منتصف السجل
            history = history.slice(0, historyStep + 1);
            
            history.push({
                state: state,
                action: action,
                timestamp: new Date().toLocaleTimeString('ar-SA')
            });
            
            historyStep = history.length - 1;
            
            updateHistoryPanel();
            updateUndoRedoButtons();
        }
        
        // تحديث لوحة السجل
        function updateHistoryPanel() {
            const panel = document.getElementById('historyPanel');
            panel.innerHTML = '';
            
            history.forEach((item, index) => {
                const div = document.createElement('div');
                div.className = 'history-item' + (index === historyStep ? ' bg-primary text-white' : '');
                div.innerHTML = `
                    <small>${item.action}</small><br>
                    <small class="text-muted">${item.timestamp}</small>
                `;
                div.onclick = () => restoreFromHistory(index);
                panel.appendChild(div);
            });
        }
        
        // استعادة من السجل
        function restoreFromHistory(index) {
            if (index >= 0 && index < history.length) {
                historyStep = index;
                canvas.loadFromJSON(history[index].state, function() {
                    canvas.renderAll();
                    updateHistoryPanel();
                    updateUndoRedoButtons();
                });
            }
        }
        
        // تراجع
        function undoAction() {
            if (historyStep > 0) {
                restoreFromHistory(historyStep - 1);
            }
        }
        
        // إعادة
        function redoAction() {
            if (historyStep < history.length - 1) {
                restoreFromHistory(historyStep + 1);
            }
        }
        
        // تحديث أزرار التراجع والإعادة
        function updateUndoRedoButtons() {
            document.getElementById('undoBtn').disabled = historyStep <= 0;
            document.getElementById('redoBtn').disabled = historyStep >= history.length - 1;
        }
        
        // إعادة تعيين الصورة
        function resetImage() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
                loadDocument();
            }
        }
        
        // حفظ الوثيقة
        function saveDocument() {
            showLoading(true);
            
            const quality = parseFloat(document.getElementById('saveQuality').value);
            const format = document.getElementById('saveFormat').value;
            const keepOriginal = document.getElementById('keepOriginal').checked;
            
            // تصدير الكانفاس كصورة
            const dataURL = canvas.toDataURL(`image/${format}`, quality);
            
            // إرسال البيانات للخادم
            const formData = new FormData();
            formData.append('document_id', documentData.id);
            formData.append('image_data', dataURL);
            formData.append('format', format);
            formData.append('quality', quality);
            formData.append('keep_original', keepOriginal ? '1' : '0');
            
            fetch('../includes/save_edited_document.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                
                if (data.success) {
                    alert('تم حفظ الوثيقة بنجاح');
                    // يمكن إعادة التوجيه أو تحديث الصفحة
                } else {
                    alert('فشل في حفظ الوثيقة: ' + data.message);
                }
            })
            .catch(error => {
                showLoading(false);
                alert('حدث خطأ في حفظ الوثيقة');
                console.error('Error:', error);
            });
        }
        
        // تصدير الوثيقة
        function exportDocument() {
            const format = document.getElementById('saveFormat').value;
            const quality = parseFloat(document.getElementById('saveQuality').value);
            
            const dataURL = canvas.toDataURL(`image/${format}`, quality);
            
            // إنشاء رابط تحميل
            const link = document.createElement('a');
            link.download = `edited_${documentData.filename}`;
            link.href = dataURL;
            link.click();
        }
        
        // تحديث معلومات الصورة
        function updateImageInfo(width, height) {
            document.getElementById('imageInfo').textContent = 
                `الأبعاد: ${width} × ${height} | التكبير: ${Math.round(currentZoom * 100)}%`;
        }
        
        // التكبير والتصغير
        function zoomIn() {
            currentZoom *= 1.2;
            canvas.setZoom(currentZoom);
            updateZoomLevel();
        }
        
        function zoomOut() {
            currentZoom /= 1.2;
            canvas.setZoom(currentZoom);
            updateZoomLevel();
        }
        
        function fitToScreen() {
            canvas.setZoom(1);
            currentZoom = 1;
            updateZoomLevel();
        }
        
        function updateZoomLevel() {
            document.getElementById('zoomLevel').textContent = Math.round(currentZoom * 100) + '%';
        }
        
        // إظهار/إخفاء شاشة التحميل
        function showLoading(show) {
            const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
            if (show) {
                modal.show();
            } else {
                modal.hide();
            }
        }
        
        // تعيين سمك القلم
        function setPenWidth(width) {
            document.getElementById('penWidthValue').textContent = width;
        }
    </script>
</body>
</html>

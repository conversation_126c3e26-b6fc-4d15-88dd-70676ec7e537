<?php
/**
 * إعدادات قاعدة البيانات
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'dossier_employes');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الاتصال
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
]);

/**
 * كلاس الاتصال بقاعدة البيانات
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على مثيل واحد من قاعدة البيانات (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("خطأ في استعلام SELECT: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام INSERT/UPDATE/DELETE
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على آخر ID مُدرج
     */
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * منع استنساخ الكائن
     */
    private function __clone() {}
    
    /**
     * منع إلغاء تسلسل الكائن
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * دالة مساعدة للحصول على اتصال قاعدة البيانات
 */
function getDB() {
    return Database::getInstance();
}

/**
 * دالة مساعدة لتنفيذ استعلامات SELECT
 */
function dbSelect($query, $params = []) {
    return Database::getInstance()->select($query, $params);
}

/**
 * دالة مساعدة لتنفيذ استعلامات INSERT/UPDATE/DELETE
 */
function dbExecute($query, $params = []) {
    return Database::getInstance()->execute($query, $params);
}

/**
 * دالة مساعدة للحصول على آخر ID
 */
function dbLastInsertId() {
    return Database::getInstance()->lastInsertId();
}
?>

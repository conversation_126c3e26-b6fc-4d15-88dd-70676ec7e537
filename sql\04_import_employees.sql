-- سكريبت استيراد بيانات الموظفين من ملف Excel
-- نظام إدارة الوثائق الإلكترونية للموظفين

USE dossier_employes;

-- تعطيل فحص المراجع الخارجية مؤقتاً لتسريع الاستيراد
SET FOREIGN_KEY_CHECKS = 0;

-- إنشاء جدول مؤقت لاستيراد البيانات من Excel
CREATE TEMPORARY TABLE temp_excel_import (
    numero VARCHAR(20),
    structure TEXT,
    photo VARCHAR(50),
    type_employe VARCHAR(100),
    nin VARCHAR(20),
    matricule VARCHAR(20),
    nom VARCHAR(100),
    prenom VARCHAR(100),
    nom_arabe VARCHAR(100),
    prenom_arabe VARCHAR(100),
    nom_jeune_fille VARCHAR(100),
    nom_jeune_fille_arabe VARCHAR(100),
    civilite VARCHAR(20),
    presume VARCHAR(20),
    date_naissance VARCHAR(20),
    lieu_naissance VARCHAR(100),
    lieu_naissance_arabe VARCHAR(100),
    situation_familiale VARCHAR(50),
    nationalite VARCHAR(50),
    service_national VARCHAR(50),
    groupe_sanguin VARCHAR(5),
    prenom_pere VARCHAR(100),
    prenom_pere_arabe VARCHAR(100),
    nom_mere VARCHAR(100),
    nom_mere_arabe VARCHAR(100),
    prenom_mere VARCHAR(100),
    prenom_mere_arabe VARCHAR(100),
    date_recrutement VARCHAR(20),
    corps VARCHAR(100),
    grade VARCHAR(100),
    filiere VARCHAR(100),
    date_installation VARCHAR(20),
    numero_securite_sociale VARCHAR(20),
    date_affiliation VARCHAR(20),
    type_compte VARCHAR(50),
    numero_compte VARCHAR(30),
    date_effet VARCHAR(20),
    echelon VARCHAR(20),
    categorie VARCHAR(50),
    position_actuelle VARCHAR(100),
    carte_rfid VARCHAR(20),
    structure_2 TEXT
);

-- ملاحظة: يجب تشغيل هذا الأمر بعد تصدير البيانات من Excel إلى CSV
-- LOAD DATA INFILE 'path/to/employees.csv'
-- INTO TABLE temp_excel_import
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- دالة لتنظيف وتحويل التواريخ
DELIMITER //
CREATE FUNCTION clean_date(date_str VARCHAR(20)) 
RETURNS DATE
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DATE DEFAULT NULL;
    
    -- محاولة تحويل التاريخ بصيغ مختلفة
    IF date_str IS NOT NULL AND date_str != '' THEN
        -- صيغة dd/mm/yyyy
        IF date_str REGEXP '^[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}$' THEN
            SET result = STR_TO_DATE(date_str, '%d/%m/%Y');
        -- صيغة yyyy-mm-dd
        ELSEIF date_str REGEXP '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$' THEN
            SET result = STR_TO_DATE(date_str, '%Y-%m-%d');
        -- صيغة dd-mm-yyyy
        ELSEIF date_str REGEXP '^[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}$' THEN
            SET result = STR_TO_DATE(date_str, '%d-%m-%Y');
        END IF;
    END IF;
    
    RETURN result;
END//
DELIMITER ;

-- سكريبت PHP لاستيراد البيانات (سيتم إنشاؤه في ملف منفصل)
-- هذا مثال على كيفية استيراد البيانات من الجدول المؤقت إلى الجدول الرئيسي

/*
INSERT INTO employees (
    employee_number,
    nin,
    matricule,
    nom,
    prenom,
    nom_arabe,
    prenom_arabe,
    date_naissance,
    lieu_naissance,
    lieu_naissance_arabe,
    groupe_sanguin,
    structure,
    type_employe,
    corps,
    grade,
    filiere,
    echelon,
    categorie,
    position_actuelle,
    date_recrutement,
    date_installation,
    numero_securite_sociale,
    numero_compte,
    carte_rfid,
    photo_path,
    status,
    created_by
)
SELECT 
    TRIM(numero) as employee_number,
    TRIM(nin) as nin,
    TRIM(matricule) as matricule,
    TRIM(UPPER(nom)) as nom,
    TRIM(UPPER(prenom)) as prenom,
    TRIM(nom_arabe) as nom_arabe,
    TRIM(prenom_arabe) as prenom_arabe,
    clean_date(date_naissance) as date_naissance,
    TRIM(lieu_naissance) as lieu_naissance,
    TRIM(lieu_naissance_arabe) as lieu_naissance_arabe,
    TRIM(groupe_sanguin) as groupe_sanguin,
    TRIM(structure) as structure,
    TRIM(type_employe) as type_employe,
    TRIM(corps) as corps,
    TRIM(grade) as grade,
    TRIM(filiere) as filiere,
    TRIM(echelon) as echelon,
    TRIM(categorie) as categorie,
    TRIM(position_actuelle) as position_actuelle,
    clean_date(date_recrutement) as date_recrutement,
    clean_date(date_installation) as date_installation,
    TRIM(numero_securite_sociale) as numero_securite_sociale,
    TRIM(numero_compte) as numero_compte,
    TRIM(carte_rfid) as carte_rfid,
    CASE 
        WHEN TRIM(photo) = 'fourni' THEN CONCAT('photos/', TRIM(matricule), '.jpg')
        ELSE NULL 
    END as photo_path,
    'active' as status,
    1 as created_by  -- المستخدم الإداري
FROM temp_excel_import
WHERE numero IS NOT NULL 
  AND numero != ''
  AND matricule IS NOT NULL 
  AND matricule != ''
  AND nom IS NOT NULL 
  AND nom != ''
ON DUPLICATE KEY UPDATE
    nom = VALUES(nom),
    prenom = VALUES(prenom),
    nom_arabe = VALUES(nom_arabe),
    prenom_arabe = VALUES(prenom_arabe),
    date_naissance = VALUES(date_naissance),
    lieu_naissance = VALUES(lieu_naissance),
    lieu_naissance_arabe = VALUES(lieu_naissance_arabe),
    groupe_sanguin = VALUES(groupe_sanguin),
    structure = VALUES(structure),
    type_employe = VALUES(type_employe),
    corps = VALUES(corps),
    grade = VALUES(grade),
    filiere = VALUES(filiere),
    echelon = VALUES(echelon),
    categorie = VALUES(categorie),
    position_actuelle = VALUES(position_actuelle),
    date_recrutement = VALUES(date_recrutement),
    date_installation = VALUES(date_installation),
    numero_securite_sociale = VALUES(numero_securite_sociale),
    numero_compte = VALUES(numero_compte),
    carte_rfid = VALUES(carte_rfid),
    updated_at = CURRENT_TIMESTAMP;
*/

-- إعادة تفعيل فحص المراجع الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- حذف الدالة المؤقتة
DROP FUNCTION IF EXISTS clean_date;

-- إحصائيات بعد الاستيراد
SELECT 
    COUNT(*) as total_employees,
    COUNT(DISTINCT structure) as total_structures,
    COUNT(DISTINCT grade) as total_grades,
    COUNT(DISTINCT corps) as total_corps
FROM employees;

-- عرض توزيع الموظفين حسب الهيكل
SELECT 
    structure,
    COUNT(*) as employee_count
FROM employees 
GROUP BY structure 
ORDER BY employee_count DESC 
LIMIT 10;

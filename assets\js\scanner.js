/**
 * مكتبة الماسح الضوئي المتقدمة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 * دعم TWAIN, WIA, SANE وماسحات الويب
 */

class DocumentScanner {
    constructor(options = {}) {
        this.options = {
            resolution: options.resolution || 300, // DPI
            colorMode: options.colorMode || 'color', // color, grayscale, blackwhite
            format: options.format || 'jpeg', // jpeg, png, pdf
            quality: options.quality || 85,
            autoDetectPages: options.autoDetectPages || true,
            ...options
        };
        
        this.isScanning = false;
        this.scannedDocuments = [];
        this.currentEmployeeId = null;
        this.currentDocumentType = null;
        
        this.initializeScanner();
    }
    
    /**
     * تهيئة الماسح الضوئي
     */
    async initializeScanner() {
        try {
            // التحقق من دعم الماسح الضوئي في المتصفح
            if ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices) {
                console.log('✅ دعم الكاميرا متوفر');
            }
            
            // التحقق من دعم Web TWAIN
            if (typeof Dynamsoft !== 'undefined' && Dynamsoft.DWT) {
                await this.initializeDynamsoft();
            }
            
            // التحقق من دعم ASPRISE Scanner
            if (typeof scanner !== 'undefined') {
                await this.initializeAsprise();
            }
            
            this.showScannerStatus('جاهز للمسح', 'success');
            
        } catch (error) {
            console.error('خطأ في تهيئة الماسح الضوئي:', error);
            this.showScannerStatus('خطأ في تهيئة الماسح الضوئي', 'error');
        }
    }
    
    /**
     * تهيئة Dynamsoft Web TWAIN
     */
    async initializeDynamsoft() {
        try {
            Dynamsoft.DWT.AutoLoad = false;
            Dynamsoft.DWT.ProductKey = 'YOUR_DYNAMSOFT_LICENSE_KEY'; // يجب الحصول على مفتاح ترخيص
            Dynamsoft.DWT.ResourcesPath = 'assets/js/dynamsoft/';
            
            await new Promise((resolve, reject) => {
                Dynamsoft.DWT.Load();
                Dynamsoft.DWT.RegisterEvent('OnWebTwainReady', () => {
                    this.dwt = Dynamsoft.DWT.GetWebTwain('dwtcontrolContainer');
                    resolve();
                });
            });
            
            console.log('✅ Dynamsoft Web TWAIN جاهز');
            return true;
        } catch (error) {
            console.warn('⚠️ Dynamsoft Web TWAIN غير متوفر:', error);
            return false;
        }
    }
    
    /**
     * تهيئة ASPRISE Scanner
     */
    async initializeAsprise() {
        try {
            scanner.setLicenseKey('YOUR_ASPRISE_LICENSE_KEY'); // يجب الحصول على مفتاح ترخيص
            console.log('✅ ASPRISE Scanner جاهز');
            return true;
        } catch (error) {
            console.warn('⚠️ ASPRISE Scanner غير متوفر:', error);
            return false;
        }
    }
    
    /**
     * بدء عملية المسح
     */
    async startScan(employeeId, documentTypeId) {
        if (this.isScanning) {
            this.showAlert('عملية مسح جارية بالفعل', 'warning');
            return;
        }
        
        this.currentEmployeeId = employeeId;
        this.currentDocumentType = documentTypeId;
        this.isScanning = true;
        
        this.showScannerStatus('جاري المسح...', 'info');
        this.updateScanButton(true);
        
        try {
            // محاولة المسح بطرق مختلفة
            let scannedImage = null;
            
            // الطريقة 1: Dynamsoft Web TWAIN
            if (this.dwt) {
                scannedImage = await this.scanWithDynamsoft();
            }
            
            // الطريقة 2: ASPRISE Scanner
            if (!scannedImage && typeof scanner !== 'undefined') {
                scannedImage = await this.scanWithAsprise();
            }
            
            // الطريقة 3: كاميرا الويب (للأجهزة المحمولة)
            if (!scannedImage) {
                scannedImage = await this.scanWithCamera();
            }
            
            if (scannedImage) {
                await this.processScanResult(scannedImage);
            } else {
                throw new Error('لم يتم العثور على ماسح ضوئي متوفر');
            }
            
        } catch (error) {
            console.error('خطأ في المسح:', error);
            this.showAlert('فشل في مسح الوثيقة: ' + error.message, 'error');
        } finally {
            this.isScanning = false;
            this.updateScanButton(false);
            this.showScannerStatus('جاهز للمسح', 'success');
        }
    }
    
    /**
     * المسح باستخدام Dynamsoft Web TWAIN
     */
    async scanWithDynamsoft() {
        return new Promise((resolve, reject) => {
            if (!this.dwt) {
                reject(new Error('Dynamsoft Web TWAIN غير متوفر'));
                return;
            }
            
            // إعدادات المسح
            this.dwt.IfShowUI = false;
            this.dwt.Resolution = this.options.resolution;
            this.dwt.PixelType = this.getPixelType();
            
            // بدء المسح
            this.dwt.AcquireImage(
                () => {
                    if (this.dwt.HowManyImagesInBuffer > 0) {
                        // تحويل الصورة إلى Base64
                        this.dwt.ConvertToBase64(
                            [this.dwt.HowManyImagesInBuffer - 1],
                            Dynamsoft.DWT.EnumDWT_ImageType.IT_JPG,
                            (result) => {
                                resolve({
                                    data: result.getData(),
                                    format: 'jpeg',
                                    source: 'dynamsoft'
                                });
                            },
                            (error) => reject(new Error('فشل في تحويل الصورة: ' + error.errorString))
                        );
                    } else {
                        reject(new Error('لم يتم مسح أي صورة'));
                    }
                },
                (error) => reject(new Error('فشل في المسح: ' + error.errorString))
            );
        });
    }
    
    /**
     * المسح باستخدام ASPRISE Scanner
     */
    async scanWithAsprise() {
        return new Promise((resolve, reject) => {
            try {
                scanner.scan({
                    'output_settings': [{
                        'type': 'return-base64',
                        'format': this.options.format,
                        'quality': this.options.quality
                    }],
                    'scanner_settings': {
                        'resolution': this.options.resolution,
                        'color_mode': this.options.colorMode
                    }
                }, (successful, mesg, response) => {
                    if (successful) {
                        const scannedImages = scanner.getScannedImages(response, true, false);
                        if (scannedImages && scannedImages.length > 0) {
                            resolve({
                                data: scannedImages[0].src,
                                format: this.options.format,
                                source: 'asprise'
                            });
                        } else {
                            reject(new Error('لم يتم مسح أي صورة'));
                        }
                    } else {
                        reject(new Error('فشل في المسح: ' + mesg));
                    }
                });
            } catch (error) {
                reject(new Error('خطأ في ASPRISE Scanner: ' + error.message));
            }
        });
    }
    
    /**
     * المسح باستخدام كاميرا الويب
     */
    async scanWithCamera() {
        return new Promise((resolve, reject) => {
            // إنشاء نافذة التقاط الصورة
            const modal = this.createCameraModal();
            document.body.appendChild(modal);
            
            navigator.mediaDevices.getUserMedia({ 
                video: { 
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    facingMode: 'environment' // الكاميرا الخلفية للهواتف
                } 
            })
            .then(stream => {
                const video = modal.querySelector('#cameraVideo');
                video.srcObject = stream;
                
                // زر التقاط الصورة
                modal.querySelector('#captureBtn').onclick = () => {
                    const canvas = modal.querySelector('#captureCanvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    ctx.drawImage(video, 0, 0);
                    
                    // تحويل إلى Base64
                    const imageData = canvas.toDataURL('image/jpeg', this.options.quality / 100);
                    
                    // إيقاف الكاميرا
                    stream.getTracks().forEach(track => track.stop());
                    document.body.removeChild(modal);
                    
                    resolve({
                        data: imageData,
                        format: 'jpeg',
                        source: 'camera'
                    });
                };
                
                // زر الإلغاء
                modal.querySelector('#cancelBtn').onclick = () => {
                    stream.getTracks().forEach(track => track.stop());
                    document.body.removeChild(modal);
                    reject(new Error('تم إلغاء المسح'));
                };
            })
            .catch(error => {
                document.body.removeChild(modal);
                reject(new Error('فشل في الوصول للكاميرا: ' + error.message));
            });
        });
    }
    
    /**
     * معالجة نتيجة المسح
     */
    async processScanResult(scannedImage) {
        try {
            // إضافة الصورة للمعاينة
            this.addScannedImageToPreview(scannedImage);
            
            // تحسين جودة الصورة (اختياري)
            if (this.options.enhanceImage) {
                scannedImage = await this.enhanceImage(scannedImage);
            }
            
            // حفظ الوثيقة
            await this.saveScannedDocument(scannedImage);
            
            this.showAlert('تم مسح الوثيقة بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في معالجة الصورة الممسوحة:', error);
            this.showAlert('فشل في معالجة الوثيقة الممسوحة', 'error');
        }
    }
    
    /**
     * حفظ الوثيقة الممسوحة
     */
    async saveScannedDocument(scannedImage) {
        const formData = new FormData();
        
        // تحويل Base64 إلى Blob
        const blob = this.base64ToBlob(scannedImage.data, `image/${scannedImage.format}`);
        
        formData.append('document', blob, `scanned_${Date.now()}.${scannedImage.format}`);
        formData.append('employee_id', this.currentEmployeeId);
        formData.append('document_type_id', this.currentDocumentType);
        formData.append('source', scannedImage.source);
        formData.append('resolution', this.options.resolution);
        formData.append('color_mode', this.options.colorMode);
        
        const response = await fetch('includes/save_scanned_document.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || 'فشل في حفظ الوثيقة');
        }
        
        return result;
    }
    
    /**
     * إضافة الصورة الممسوحة للمعاينة
     */
    addScannedImageToPreview(scannedImage) {
        const previewContainer = document.getElementById('scannedPreview');
        if (!previewContainer) return;
        
        const imageElement = document.createElement('div');
        imageElement.className = 'scanned-image-preview';
        imageElement.innerHTML = `
            <div class="image-container">
                <img src="${scannedImage.data}" alt="وثيقة ممسوحة" class="img-fluid">
                <div class="image-controls">
                    <button class="btn btn-sm btn-primary" onclick="editScannedImage(this)">
                        <i class="bi bi-pencil"></i> تحرير
                    </button>
                    <button class="btn btn-sm btn-success" onclick="saveScannedImage(this)">
                        <i class="bi bi-check"></i> حفظ
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteScannedImage(this)">
                        <i class="bi bi-trash"></i> حذف
                    </button>
                </div>
            </div>
            <div class="image-info">
                <small class="text-muted">
                    المصدر: ${scannedImage.source} | 
                    الدقة: ${this.options.resolution} DPI |
                    التنسيق: ${scannedImage.format.toUpperCase()}
                </small>
            </div>
        `;
        
        previewContainer.appendChild(imageElement);
        this.scannedDocuments.push(scannedImage);
    }
    
    /**
     * إنشاء نافذة الكاميرا
     */
    createCameraModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.style.backgroundColor = 'rgba(0,0,0,0.8)';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">مسح الوثيقة بالكاميرا</h5>
                    </div>
                    <div class="modal-body text-center">
                        <video id="cameraVideo" autoplay playsinline style="width: 100%; max-width: 500px;"></video>
                        <canvas id="captureCanvas" style="display: none;"></canvas>
                        <div class="mt-3">
                            <button id="captureBtn" class="btn btn-primary me-2">
                                <i class="bi bi-camera"></i> التقاط الصورة
                            </button>
                            <button id="cancelBtn" class="btn btn-secondary">
                                <i class="bi bi-x"></i> إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return modal;
    }
    
    /**
     * مساعدات مختلفة
     */
    getPixelType() {
        switch (this.options.colorMode) {
            case 'blackwhite': return Dynamsoft.DWT.EnumDWT_PixelType.TWPT_BW;
            case 'grayscale': return Dynamsoft.DWT.EnumDWT_PixelType.TWPT_GRAY;
            default: return Dynamsoft.DWT.EnumDWT_PixelType.TWPT_RGB;
        }
    }
    
    base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64.split(',')[1]);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }
    
    showScannerStatus(message, type) {
        const statusElement = document.getElementById('scannerStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `alert alert-${type}`;
        }
    }
    
    updateScanButton(isScanning) {
        const scanBtn = document.getElementById('scanBtn');
        if (scanBtn) {
            scanBtn.disabled = isScanning;
            scanBtn.innerHTML = isScanning ? 
                '<i class="bi bi-hourglass-split"></i> جاري المسح...' : 
                '<i class="bi bi-scanner"></i> مسح وثيقة';
        }
    }
    
    showAlert(message, type) {
        // يمكن استخدام مكتبة إشعارات مثل Toastr أو SweetAlert
        alert(message);
    }
}

// إنشاء مثيل عام للماسح الضوئي
let documentScanner;

// تهيئة الماسح الضوئي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    documentScanner = new DocumentScanner({
        resolution: 300,
        colorMode: 'color',
        format: 'jpeg',
        quality: 85,
        enhanceImage: true
    });
});

// دوال مساعدة للواجهة
function startDocumentScan(employeeId, documentTypeId) {
    if (documentScanner) {
        documentScanner.startScan(employeeId, documentTypeId);
    } else {
        alert('الماسح الضوئي غير جاهز');
    }
}

function editScannedImage(button) {
    // سيتم تطوير محرر الصور
    alert('محرر الصور قيد التطوير');
}

function saveScannedImage(button) {
    // حفظ الصورة المحررة
    alert('تم حفظ الصورة');
}

function deleteScannedImage(button) {
    if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟')) {
        button.closest('.scanned-image-preview').remove();
    }
}

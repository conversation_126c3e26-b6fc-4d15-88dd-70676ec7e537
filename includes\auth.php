<?php
/**
 * نظام المصادقة والصلاحيات
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';

/**
 * كلاس إدارة المصادقة
 */
class Auth {
    
    /**
     * تسجيل دخول المستخدم
     */
    public static function login($username, $password) {
        try {
            // البحث عن المستخدم
            $query = "SELECT id, username, password_hash, full_name, role, is_active, 
                             failed_login_attempts, locked_until 
                      FROM users 
                      WHERE username = ? AND is_active = 1";
            
            $user = dbSelect($query, [$username]);
            
            if (empty($user)) {
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            $user = $user[0];
            
            // التحقق من قفل الحساب
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                $remaining = ceil((strtotime($user['locked_until']) - time()) / 60);
                return ['success' => false, 'message' => "الحساب مقفل لمدة {$remaining} دقيقة"];
            }
            
            // التحقق من كلمة المرور
            if (!verifyPassword($password, $user['password_hash'])) {
                self::handleFailedLogin($user['id']);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // تسجيل دخول ناجح
            self::createSession($user);
            self::resetFailedAttempts($user['id']);
            self::updateLastLogin($user['id']);
            
            logActivity($user['id'], 'login', 'users', $user['id'], 'تسجيل دخول ناجح');
            
            return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح'];
            
        } catch (Exception $e) {
            logError("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في النظام'];
        }
    }
    
    /**
     * إنشاء جلسة المستخدم
     */
    private static function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // إنشاء رمز الجلسة
        $session_id = session_id();
        $query = "INSERT INTO sessions (id, user_id, ip_address, user_agent, payload, last_activity) 
                  VALUES (?, ?, ?, ?, ?, ?) 
                  ON DUPLICATE KEY UPDATE 
                  last_activity = VALUES(last_activity), 
                  payload = VALUES(payload)";
        
        $payload = json_encode($_SESSION);
        $params = [
            $session_id,
            $user['id'],
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $payload,
            time()
        ];
        
        dbExecute($query, $params);
    }
    
    /**
     * معالجة محاولة تسجيل دخول فاشلة
     */
    private static function handleFailedLogin($user_id) {
        $query = "UPDATE users 
                  SET failed_login_attempts = failed_login_attempts + 1,
                      locked_until = CASE 
                          WHEN failed_login_attempts + 1 >= ? THEN DATE_ADD(NOW(), INTERVAL ? SECOND)
                          ELSE locked_until 
                      END
                  WHERE id = ?";
        
        dbExecute($query, [MAX_LOGIN_ATTEMPTS, LOCKOUT_DURATION, $user_id]);
        
        logActivity($user_id, 'failed_login', 'users', $user_id, 'محاولة تسجيل دخول فاشلة');
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     */
    private static function resetFailedAttempts($user_id) {
        $query = "UPDATE users 
                  SET failed_login_attempts = 0, locked_until = NULL 
                  WHERE id = ?";
        
        dbExecute($query, [$user_id]);
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private static function updateLastLogin($user_id) {
        $query = "UPDATE users SET last_login = NOW() WHERE id = ?";
        dbExecute($query, [$user_id]);
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public static function logout() {
        if (isset($_SESSION['user_id'])) {
            logActivity($_SESSION['user_id'], 'logout', 'users', $_SESSION['user_id'], 'تسجيل خروج');
            
            // حذف الجلسة من قاعدة البيانات
            $session_id = session_id();
            dbExecute("DELETE FROM sessions WHERE id = ?", [$session_id]);
        }
        
        // تدمير الجلسة
        session_destroy();
        session_start();
        session_regenerate_id(true);
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public static function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['login_time'])) {
            return false;
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            self::logout();
            return false;
        }
        
        // تحديث آخر نشاط
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * الحصول على معلومات المستخدم الحالي
     */
    public static function getCurrentUser() {
        if (!self::isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'full_name' => $_SESSION['full_name'],
            'role' => $_SESSION['role']
        ];
    }
    
    /**
     * التحقق من الصلاحية
     */
    public static function hasPermission($permission) {
        if (!self::isLoggedIn()) {
            return false;
        }
        
        $role = $_SESSION['role'];
        
        // صلاحيات الأدوار
        $permissions = [
            'admin' => ['*'], // جميع الصلاحيات
            'hr_manager' => [
                'view_employees', 'add_employee', 'edit_employee', 'delete_employee',
                'view_documents', 'add_document', 'edit_document', 'delete_document',
                'view_reports', 'manage_users'
            ],
            'hr_employee' => [
                'view_employees', 'add_employee', 'edit_employee',
                'view_documents', 'add_document', 'edit_document'
            ],
            'archivist' => [
                'view_employees', 'view_documents', 'add_document', 'edit_document'
            ],
            'viewer' => [
                'view_employees', 'view_documents'
            ]
        ];
        
        // المدير له جميع الصلاحيات
        if ($role === 'admin') {
            return true;
        }
        
        // التحقق من الصلاحية المحددة
        return isset($permissions[$role]) && in_array($permission, $permissions[$role]);
    }
    
    /**
     * إجبار تسجيل الدخول
     */
    public static function requireLogin() {
        if (!self::isLoggedIn()) {
            header('Location: ' . BASE_URL . 'pages/login.php');
            exit;
        }
    }
    
    /**
     * إجبار صلاحية معينة
     */
    public static function requirePermission($permission) {
        self::requireLogin();
        
        if (!self::hasPermission($permission)) {
            header('HTTP/1.1 403 Forbidden');
            die('ليس لديك صلاحية للوصول إلى هذه الصفحة');
        }
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public static function cleanExpiredSessions() {
        $expired_time = time() - SESSION_TIMEOUT;
        dbExecute("DELETE FROM sessions WHERE last_activity < ?", [$expired_time]);
    }
}

/**
 * دوال مساعدة للمصادقة
 */

function isLoggedIn() {
    return Auth::isLoggedIn();
}

function getCurrentUser() {
    return Auth::getCurrentUser();
}

function hasPermission($permission) {
    return Auth::hasPermission($permission);
}

function requireLogin() {
    Auth::requireLogin();
}

function requirePermission($permission) {
    Auth::requirePermission($permission);
}

// تنظيف الجلسات المنتهية الصلاحية (يتم تشغيلها أحياناً)
if (rand(1, 100) <= 5) { // 5% احتمال
    Auth::cleanExpiredSessions();
}
?>

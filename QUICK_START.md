# 🚀 دليل البدء السريع - نظام إدارة الوثائق الإلكترونية

## ⚡ خطوات سريعة للتشغيل

### 1. تشغيل XAMPP
```
1. افتح XAMPP Control Panel
2. اضغط "Start" بجانب Apache
3. اضغط "Start" بجانب MySQL
4. تأكد من ظهور اللون الأخضر
```

### 2. إنشاء قاعدة البيانات
```
1. افتح المتصفح واذهب إلى: http://localhost/phpmyadmin
2. اضغط "New" لإنشاء قاعدة بيانات جديدة
3. اكتب اسم قاعدة البيانات: dossier_employes
4. اختر Collation: utf8mb4_unicode_ci
5. اضغط "Create"
```

### 3. استيراد هيكل قاعدة البيانات
```
1. في phpMyAdmin، اختر قاعدة البيانات dossier_employes
2. اضغط على تبويب "Import"
3. اضغط "Choose File" واختر الملفات بالترتيب:
   - sql/01_create_database.sql
   - sql/02_create_tables.sql  
   - sql/03_insert_document_types.sql
4. اضغط "Go" لكل ملف
```

### 4. تثبيت Python (لاستيراد Excel)
```
1. حمل Python من: https://www.python.org/downloads/
2. ثبت Python مع تفعيل "Add to PATH"
3. افتح Command Prompt واكتب:
   pip install pandas openpyxl
```

### 5. تشغيل النظام
```
1. افتح المتصفح
2. اذهب إلى: http://localhost/Edocs/pages/login.php
3. استخدم أحد الحسابات التجريبية:
   - المدير: admin / password
   - مسؤول الموارد البشرية: hr_manager / password
   - مسؤول الأرشيف: archivist / password
```

## 📊 استيراد بيانات الموظفين الموجودة

### الطريقة الأولى: عبر الواجهة (موصى بها)
```
1. سجل دخول بحساب admin
2. اذهب إلى لوحة التحكم
3. اضغط "استيراد بيانات الموظفين"
4. انتظر حتى اكتمال العملية
```

### الطريقة الثانية: يدوياً
```
1. افتح Command Prompt في مجلد المشروع
2. اكتب: python includes/import_excel.py
3. انتظر رسالة "تم الاستيراد بنجاح"
```

## 🔧 إعدادات سريعة

### تغيير كلمات المرور الافتراضية
```
1. سجل دخول بحساب admin
2. اذهب إلى "إدارة المستخدمين"
3. اضغط "تعديل" بجانب كل مستخدم
4. غير كلمة المرور
5. احفظ التغييرات
```

### إنشاء مجلدات التحميل
```
المجلدات التالية ستُنشأ تلقائياً:
- uploads/documents/ (للوثائق)
- uploads/photos/ (للصور الشخصية)  
- uploads/temp/ (للملفات المؤقتة)
```

## 🎯 الاستخدام الأساسي

### إضافة موظف جديد
```
1. لوحة التحكم → "إدارة الموظفين"
2. "إضافة موظف جديد"
3. املأ البيانات الأساسية
4. ارفع الصورة الشخصية (اختياري)
5. احفظ
```

### رفع وثيقة لموظف
```
1. ابحث عن الموظف
2. اضغط "عرض الملف"
3. "رفع وثيقة جديدة"
4. اختر نوع الوثيقة
5. اسحب الملف أو اختره
6. احفظ
```

### البحث عن موظف
```
1. استخدم مربع البحث في الأعلى
2. اكتب الاسم أو رقم التسجيل
3. أو استخدم الفلاتر المتقدمة:
   - الهيكل/المؤسسة
   - الرتبة
   - السلك
   - تاريخ التوظيف
```

## ⚠️ مشاكل شائعة وحلولها

### "خطأ في الاتصال بقاعدة البيانات"
```
الحل:
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات قاعدة البيانات في config/database.php
3. تأكد من وجود قاعدة البيانات dossier_employes
```

### "فشل في رفع الملف"
```
الحل:
1. تحقق من وجود مجلد uploads
2. تأكد من صلاحيات الكتابة
3. تحقق من حجم الملف (أقل من 10MB)
4. تأكد من نوع الملف المسموح
```

### "Python غير متوفر"
```
الحل:
1. ثبت Python من الموقع الرسمي
2. تأكد من إضافة Python إلى PATH
3. ثبت المكتبات المطلوبة: pip install pandas openpyxl
```

### "صفحة فارغة أو خطأ 500"
```
الحل:
1. تحقق من ملف logs/error.log
2. تأكد من إصدار PHP (8.0+)
3. فعل عرض الأخطاء في PHP
4. تحقق من صلاحيات الملفات
```

## 📱 روابط مهمة

- **تسجيل الدخول**: http://localhost/Edocs/pages/login.php
- **لوحة التحكم**: http://localhost/Edocs/pages/dashboard.php
- **phpMyAdmin**: http://localhost/phpmyadmin
- **XAMPP Control**: C:\xampp\xampp-control.exe

## 📞 الحصول على المساعدة

### تحقق من السجلات
```
- أخطاء PHP: C:\xampp\apache\logs\error.log
- أخطاء النظام: logs/error.log
- سجل العمليات: جدول audit_log في قاعدة البيانات
```

### معلومات النظام
```
- الإصدار: 1.0.0
- PHP المطلوب: 8.0+
- MySQL المطلوب: 8.0+ أو MariaDB 10.4+
- المتصفحات المدعومة: Chrome, Firefox, Edge, Safari
```

---

## ✅ قائمة التحقق السريع

- [ ] XAMPP مثبت ويعمل
- [ ] Apache يعمل (أخضر في XAMPP)
- [ ] MySQL يعمل (أخضر في XAMPP)
- [ ] قاعدة البيانات dossier_employes موجودة
- [ ] الجداول مُنشأة (6 جداول)
- [ ] Python مثبت مع pandas
- [ ] مجلدات uploads موجودة
- [ ] يمكن الوصول للنظام عبر المتصفح
- [ ] تم تغيير كلمات المرور الافتراضية

**🎉 إذا تم تحقيق جميع النقاط، فالنظام جاهز للاستخدام!**

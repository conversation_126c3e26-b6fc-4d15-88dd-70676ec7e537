<?php
/**
 * ملف الإعدادات العامة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات عامة للتطبيق
define('APP_NAME', 'نظام إدارة الوثائق الإلكترونية');
define('APP_NAME_EN', 'Electronic Document Management System');
define('APP_VERSION', '1.0.0');
define('APP_AUTHOR', 'Direction des Œuvres Universitaires - M\'sila');

// إعدادات المسارات
define('ROOT_PATH', dirname(__DIR__));
define('BASE_URL', 'http://localhost/Edocs/');
define('ASSETS_URL', BASE_URL . 'assets/');
define('UPLOADS_URL', BASE_URL . 'uploads/');

// مسارات الملفات
define('UPLOADS_PATH', ROOT_PATH . '/uploads/');
define('DOCUMENTS_PATH', UPLOADS_PATH . 'documents/');
define('PHOTOS_PATH', UPLOADS_PATH . 'photos/');
define('TEMP_PATH', UPLOADS_PATH . 'temp/');

// إعدادات رفع الملفات
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 MB
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx']);
define('ALLOWED_PHOTO_TYPES', ['jpg', 'jpeg', 'png']);

// إعدادات الأمان
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 دقيقة

// إعدادات التصفح
define('RECORDS_PER_PAGE', 20);
define('MAX_SEARCH_RESULTS', 100);

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'fr', 'en']);

// إعدادات التاريخ والوقت
date_default_timezone_set('Africa/Algiers');
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات قاعدة البيانات
require_once 'database.php';

/**
 * دالة لإنشاء المجلدات المطلوبة
 */
function createRequiredDirectories() {
    $directories = [
        UPLOADS_PATH,
        DOCUMENTS_PATH,
        PHOTOS_PATH,
        TEMP_PATH
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            // إنشاء ملف .htaccess لحماية المجلد
            file_put_contents($dir . '.htaccess', "Options -Indexes\nDeny from all");
        }
    }
}

/**
 * دالة لتنظيف المدخلات
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة لتوليد كلمة مرور عشوائية
 */
function generateRandomPassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

/**
 * دالة لتشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة لتوليد رمز CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * دالة للتحقق من رمز CSRF
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * دالة لتسجيل الأخطاء
 */
function logError($message, $file = '', $line = '') {
    $log = date('Y-m-d H:i:s') . " - ";
    if ($file) $log .= "File: $file - ";
    if ($line) $log .= "Line: $line - ";
    $log .= "Error: $message" . PHP_EOL;
    
    error_log($log, 3, ROOT_PATH . '/logs/error.log');
}

/**
 * دالة لتسجيل العمليات
 */
function logActivity($user_id, $action, $table_name = '', $record_id = '', $details = '') {
    try {
        $query = "INSERT INTO audit_log (user_id, action, table_name, record_id, new_values, ip_address, user_agent) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $user_id,
            $action,
            $table_name,
            $record_id,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        dbExecute($query, $params);
    } catch (Exception $e) {
        logError("Failed to log activity: " . $e->getMessage());
    }
}

/**
 * دالة لتحويل التاريخ إلى الصيغة المطلوبة
 */
function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date) || $date == '0000-00-00') {
        return '';
    }
    
    try {
        $dateObj = new DateTime($date);
        return $dateObj->format($format);
    } catch (Exception $e) {
        return $date;
    }
}

/**
 * دالة لتحويل حجم الملف إلى صيغة قابلة للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * دالة للحصول على امتداد الملف
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * دالة للتحقق من نوع الملف المسموح
 */
function isAllowedFileType($filename, $type = 'document') {
    $extension = getFileExtension($filename);
    
    if ($type === 'photo') {
        return in_array($extension, ALLOWED_PHOTO_TYPES);
    }
    
    return in_array($extension, ALLOWED_DOCUMENT_TYPES);
}

/**
 * دالة لإنشاء اسم ملف فريد
 */
function generateUniqueFilename($originalName) {
    $extension = getFileExtension($originalName);
    $basename = pathinfo($originalName, PATHINFO_FILENAME);
    $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
    
    return $basename . '_' . time() . '_' . uniqid() . '.' . $extension;
}

// إنشاء المجلدات المطلوبة
createRequiredDirectories();

// تعيين معالج الأخطاء
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    logError($message, $file, $line);
    return true;
});

// تعيين معالج الاستثناءات
set_exception_handler(function($exception) {
    logError($exception->getMessage(), $exception->getFile(), $exception->getLine());
    
    // في بيئة الإنتاج، عرض رسالة عامة
    if (defined('PRODUCTION') && PRODUCTION) {
        die('حدث خطأ في النظام. يرجى المحاولة لاحقاً.');
    } else {
        die('خطأ: ' . $exception->getMessage());
    }
});
?>

<?php
/**
 * صفحة الماسح الضوئي وعرض الوثائق
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('add_document')) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$page_title = 'الماسح الضوئي وإدارة الوثائق';

// الحصول على معرف الموظف من الرابط
$employee_id = isset($_GET['employee_id']) ? (int)$_GET['employee_id'] : 0;

// الحصول على بيانات الموظف
$employee = null;
if ($employee_id > 0) {
    $employee = $db->fetchOne("
        SELECT id, nom, prenom, matricule, structure, poste 
        FROM employees 
        WHERE id = ?
    ", [$employee_id]);
}

// الحصول على أنواع الوثائق
$document_types = $db->fetchAll("
    SELECT id, type_name, type_name_arabe, category, description 
    FROM document_types 
    WHERE is_active = 1 
    ORDER BY category, type_name_arabe
");

// تجميع أنواع الوثائق حسب الفئة
$grouped_types = [];
foreach ($document_types as $type) {
    $grouped_types[$type['category']][] = $type;
}

// الحصول على الوثائق الممسوحة للموظف
$scanned_documents = [];
if ($employee_id > 0) {
    $scanned_documents = $db->fetchAll("
        SELECT d.*, dt.type_name_arabe, dt.category,
               u.full_name as uploaded_by_name
        FROM documents d
        JOIN document_types dt ON d.document_type_id = dt.id
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.employee_id = ? AND d.is_scanned = 1
        ORDER BY d.upload_date DESC
    ", [$employee_id]);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام إدارة الوثائق</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Scanner Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/dynamsoft-javascript-barcode@9.6.20/dist/dbr.js"></script>
    
    <style>
        .scanner-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
        }
        
        .scanner-controls {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
        }
        
        .document-preview {
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            min-height: 300px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .document-preview.dragover {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.1);
        }
        
        .scanned-image-preview {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-container {
            position: relative;
            display: inline-block;
        }
        
        .image-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            border-radius: 5px;
            padding: 5px;
        }
        
        .image-controls .btn {
            margin: 2px;
            padding: 5px 10px;
        }
        
        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .document-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .document-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .document-thumbnail {
            width: 100%;
            height: 150px;
            object-fit: cover;
            background: #f8f9fa;
        }
        
        .scanner-status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            font-weight: 600;
        }
        
        .resolution-selector {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
        }
        
        .resolution-selector option {
            background: #333;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="dashboard.php">
                            <i class="bi bi-scanner"></i> الماسح الضوئي
                        </a>
                        <div class="navbar-nav ms-auto">
                            <a class="nav-link" href="dashboard.php">
                                <i class="bi bi-house"></i> الرئيسية
                            </a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <!-- معلومات الموظف -->
                <?php if ($employee): ?>
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-person-circle"></i>
                            معلومات الموظف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>الاسم:</strong> <?php echo htmlspecialchars($employee['nom'] . ' ' . $employee['prenom']); ?>
                            </div>
                            <div class="col-md-3">
                                <strong>رقم التسجيل:</strong> <?php echo htmlspecialchars($employee['matricule']); ?>
                            </div>
                            <div class="col-md-3">
                                <strong>الهيكل:</strong> <?php echo htmlspecialchars($employee['structure']); ?>
                            </div>
                            <div class="col-md-3">
                                <strong>المنصب:</strong> <?php echo htmlspecialchars($employee['poste']); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    يرجى اختيار موظف أولاً من <a href="employees.php" class="alert-link">قائمة الموظفين</a>
                </div>
                <?php endif; ?>
                
                <!-- واجهة الماسح الضوئي -->
                <div class="scanner-container">
                    <div class="row">
                        <div class="col-md-6">
                            <h3><i class="bi bi-scanner"></i> الماسح الضوئي</h3>
                            <p>امسح الوثائق مباشرة إلى النظام باستخدام الماسح الضوئي أو الكاميرا</p>
                            
                            <div class="scanner-controls">
                                <!-- نوع الوثيقة -->
                                <div class="mb-3">
                                    <label class="form-label">نوع الوثيقة:</label>
                                    <select id="documentTypeSelect" class="form-select">
                                        <option value="">اختر نوع الوثيقة</option>
                                        <?php foreach ($grouped_types as $category => $types): ?>
                                            <optgroup label="<?php echo htmlspecialchars($category); ?>">
                                                <?php foreach ($types as $type): ?>
                                                    <option value="<?php echo $type['id']; ?>">
                                                        <?php echo htmlspecialchars($type['type_name_arabe']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </optgroup>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <!-- إعدادات المسح -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">الدقة (DPI):</label>
                                        <select id="resolutionSelect" class="form-select resolution-selector">
                                            <option value="150">150 DPI - سريع</option>
                                            <option value="300" selected>300 DPI - عادي</option>
                                            <option value="600">600 DPI - عالي الجودة</option>
                                            <option value="1200">1200 DPI - جودة فائقة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">نمط الألوان:</label>
                                        <select id="colorModeSelect" class="form-select resolution-selector">
                                            <option value="color">ملون</option>
                                            <option value="grayscale">رمادي</option>
                                            <option value="blackwhite">أبيض وأسود</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">التنسيق:</label>
                                        <select id="formatSelect" class="form-select resolution-selector">
                                            <option value="jpeg">JPEG</option>
                                            <option value="png">PNG</option>
                                            <option value="pdf">PDF</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- أزرار التحكم -->
                                <div class="d-grid gap-2 d-md-flex">
                                    <button id="scanBtn" class="btn btn-light btn-lg me-md-2" 
                                            onclick="startDocumentScan(<?php echo $employee_id; ?>, document.getElementById('documentTypeSelect').value)"
                                            <?php echo $employee_id ? '' : 'disabled'; ?>>
                                        <i class="bi bi-scanner"></i> مسح وثيقة
                                    </button>
                                    <button class="btn btn-outline-light" onclick="openCameraScanner()">
                                        <i class="bi bi-camera"></i> استخدام الكاميرا
                                    </button>
                                    <button class="btn btn-outline-light" onclick="uploadFromFile()">
                                        <i class="bi bi-upload"></i> رفع ملف
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <!-- حالة الماسح الضوئي -->
                            <div id="scannerStatus" class="scanner-status alert alert-info">
                                <i class="bi bi-info-circle"></i> جاري تهيئة الماسح الضوئي...
                            </div>
                            
                            <!-- معاينة الوثائق الممسوحة -->
                            <div id="scannedPreview" class="document-preview">
                                <i class="bi bi-file-earmark-image" style="font-size: 3rem; color: #6c757d;"></i>
                                <h5 class="mt-3">معاينة الوثائق الممسوحة</h5>
                                <p class="text-muted">ستظهر الوثائق الممسوحة هنا</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الوثائق الممسوحة الموجودة -->
                <?php if (!empty($scanned_documents)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-files"></i>
                            الوثائق الممسوحة (<?php echo count($scanned_documents); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="document-grid">
                            <?php foreach ($scanned_documents as $doc): ?>
                            <div class="document-card">
                                <?php
                                $thumbnail_path = '../uploads' . $doc['file_path'];
                                $is_image = in_array($doc['file_type'], ['image/jpeg', 'image/jpg', 'image/png']);
                                ?>
                                
                                <div class="position-relative">
                                    <?php if ($is_image): ?>
                                        <img src="<?php echo htmlspecialchars($thumbnail_path); ?>" 
                                             class="document-thumbnail" 
                                             alt="<?php echo htmlspecialchars($doc['type_name_arabe']); ?>"
                                             onclick="viewDocument(<?php echo $doc['id']; ?>)">
                                    <?php else: ?>
                                        <div class="document-thumbnail d-flex align-items-center justify-content-center">
                                            <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: #dc3545;"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="position-absolute top-0 end-0 p-2">
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($doc['category']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="p-3">
                                    <h6 class="card-title"><?php echo htmlspecialchars($doc['type_name_arabe']); ?></h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($doc['upload_date'])); ?><br>
                                            <i class="bi bi-file-earmark"></i> <?php echo formatFileSize($doc['file_size']); ?><br>
                                            <i class="bi bi-person"></i> <?php echo htmlspecialchars($doc['uploaded_by_name']); ?>
                                        </small>
                                    </p>
                                    
                                    <div class="btn-group w-100" role="group">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewDocument(<?php echo $doc['id']; ?>)">
                                            <i class="bi bi-eye"></i> عرض
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editDocument(<?php echo $doc['id']; ?>)">
                                            <i class="bi bi-pencil"></i> تحرير
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteDocument(<?php echo $doc['id']; ?>)">
                                            <i class="bi bi-trash"></i> حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Modal لعرض الوثائق -->
    <div class="modal fade" id="documentViewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عرض الوثيقة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="documentViewContent">
                    <!-- محتوى الوثيقة -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="downloadDocument()">
                        <i class="bi bi-download"></i> تحميل
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- رفع الملفات المخفي -->
    <input type="file" id="fileUpload" style="display: none;" accept="image/*,.pdf" multiple>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/scanner.js"></script>
    
    <script>
        let currentDocumentId = null;
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة رفع الملفات
            document.getElementById('fileUpload').addEventListener('change', handleFileUpload);
            
            // تحديث إعدادات الماسح الضوئي
            updateScannerSettings();
        });
        
        // تحديث إعدادات الماسح الضوئي
        function updateScannerSettings() {
            if (typeof documentScanner !== 'undefined') {
                documentScanner.options.resolution = parseInt(document.getElementById('resolutionSelect').value);
                documentScanner.options.colorMode = document.getElementById('colorModeSelect').value;
                documentScanner.options.format = document.getElementById('formatSelect').value;
            }
        }
        
        // فتح الكاميرا للمسح
        function openCameraScanner() {
            const employeeId = <?php echo $employee_id; ?>;
            const documentType = document.getElementById('documentTypeSelect').value;
            
            if (!employeeId) {
                alert('يرجى اختيار موظف أولاً');
                return;
            }
            
            if (!documentType) {
                alert('يرجى اختيار نوع الوثيقة');
                return;
            }
            
            updateScannerSettings();
            startDocumentScan(employeeId, documentType);
        }
        
        // رفع ملف من الجهاز
        function uploadFromFile() {
            const employeeId = <?php echo $employee_id; ?>;
            const documentType = document.getElementById('documentTypeSelect').value;
            
            if (!employeeId) {
                alert('يرجى اختيار موظف أولاً');
                return;
            }
            
            if (!documentType) {
                alert('يرجى اختيار نوع الوثيقة');
                return;
            }
            
            document.getElementById('fileUpload').click();
        }
        
        // معالجة رفع الملفات
        function handleFileUpload(event) {
            const files = event.target.files;
            const employeeId = <?php echo $employee_id; ?>;
            const documentType = document.getElementById('documentTypeSelect').value;
            
            for (let file of files) {
                uploadFile(file, employeeId, documentType);
            }
        }
        
        // رفع ملف واحد
        async function uploadFile(file, employeeId, documentType) {
            const formData = new FormData();
            formData.append('document', file);
            formData.append('employee_id', employeeId);
            formData.append('document_type_id', documentType);
            formData.append('source', 'file_upload');
            
            try {
                const response = await fetch('../includes/save_scanned_document.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('تم رفع الملف بنجاح', 'success');
                    location.reload(); // إعادة تحميل الصفحة لعرض الملف الجديد
                } else {
                    showAlert('فشل في رفع الملف: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('خطأ في رفع الملف: ' + error.message, 'error');
            }
        }
        
        // عرض الوثيقة
        function viewDocument(documentId) {
            currentDocumentId = documentId;
            
            // جلب تفاصيل الوثيقة
            fetch(`../includes/get_document.php?id=${documentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const doc = data.document;
                        const content = document.getElementById('documentViewContent');
                        
                        if (doc.file_type.startsWith('image/')) {
                            content.innerHTML = `
                                <div class="text-center">
                                    <img src="../uploads${doc.file_path}" 
                                         class="img-fluid" 
                                         style="max-height: 70vh;"
                                         alt="${doc.type_name_arabe}">
                                </div>
                            `;
                        } else if (doc.file_type === 'application/pdf') {
                            content.innerHTML = `
                                <embed src="../uploads${doc.file_path}" 
                                       type="application/pdf" 
                                       width="100%" 
                                       height="500px">
                            `;
                        }
                        
                        new bootstrap.Modal(document.getElementById('documentViewModal')).show();
                    }
                })
                .catch(error => {
                    showAlert('خطأ في جلب الوثيقة', 'error');
                });
        }
        
        // تحرير الوثيقة
        function editDocument(documentId) {
            // سيتم تطوير محرر الوثائق
            window.open(`document_editor.php?id=${documentId}`, '_blank');
        }
        
        // حذف الوثيقة
        function deleteDocument(documentId) {
            if (confirm('هل أنت متأكد من حذف هذه الوثيقة؟')) {
                fetch('../includes/delete_document.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ document_id: documentId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('تم حذف الوثيقة بنجاح', 'success');
                        location.reload();
                    } else {
                        showAlert('فشل في حذف الوثيقة: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showAlert('خطأ في حذف الوثيقة', 'error');
                });
            }
        }
        
        // تحميل الوثيقة
        function downloadDocument() {
            if (currentDocumentId) {
                window.open(`../includes/download_document.php?id=${currentDocumentId}`, '_blank');
            }
        }
        
        // عرض التنبيهات
        function showAlert(message, type) {
            // يمكن استخدام مكتبة إشعارات أفضل
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            const container = document.querySelector('.container-fluid');
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
        
        // تحديث إعدادات الماسح عند تغيير القيم
        document.getElementById('resolutionSelect').addEventListener('change', updateScannerSettings);
        document.getElementById('colorModeSelect').addEventListener('change', updateScannerSettings);
        document.getElementById('formatSelect').addEventListener('change', updateScannerSettings);
    </script>
</body>
</html>

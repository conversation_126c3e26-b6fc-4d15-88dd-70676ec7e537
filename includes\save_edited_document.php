<?php
/**
 * حفظ الوثيقة المحررة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('edit_document')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذه العملية']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

try {
    $db = Database::getInstance();
    
    // التحقق من البيانات المطلوبة
    $required_fields = ['document_id', 'image_data', 'format'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("الحقل $field مطلوب");
        }
    }
    
    $document_id = (int)$_POST['document_id'];
    $image_data = $_POST['image_data'];
    $format = $_POST['format'];
    $quality = isset($_POST['quality']) ? (float)$_POST['quality'] : 0.8;
    $keep_original = isset($_POST['keep_original']) && $_POST['keep_original'] === '1';
    
    // التحقق من صحة التنسيق
    $allowed_formats = ['jpeg', 'png', 'pdf'];
    if (!in_array($format, $allowed_formats)) {
        throw new Exception('تنسيق الصورة غير مدعوم');
    }
    
    // جلب تفاصيل الوثيقة الأصلية
    $document = $db->fetchOne("
        SELECT d.*, 
               dt.type_name_arabe,
               e.nom, e.prenom, e.matricule
        FROM documents d
        JOIN document_types dt ON d.document_type_id = dt.id
        JOIN employees e ON d.employee_id = e.id
        WHERE d.id = ?
    ", [$document_id]);
    
    if (!$document) {
        throw new Exception('الوثيقة غير موجودة');
    }
    
    // التحقق من صلاحية تحرير الوثيقة
    if (!canEditDocument($document)) {
        throw new Exception('غير مصرح لك بتحرير هذه الوثيقة');
    }
    
    // فك تشفير بيانات الصورة
    if (strpos($image_data, 'data:image') !== 0) {
        throw new Exception('بيانات الصورة غير صحيحة');
    }
    
    // استخراج البيانات الثنائية من Base64
    $image_parts = explode(',', $image_data);
    if (count($image_parts) !== 2) {
        throw new Exception('تنسيق بيانات الصورة غير صحيح');
    }
    
    $image_binary = base64_decode($image_parts[1]);
    if ($image_binary === false) {
        throw new Exception('فشل في فك تشفير بيانات الصورة');
    }
    
    // إنشاء مجلد النسخ المحررة إذا لم يكن موجوداً
    $employee_folder = UPLOAD_PATH . '/documents/' . $document['employee_id'];
    $edited_folder = $employee_folder . '/edited';
    
    if (!is_dir($edited_folder)) {
        if (!mkdir($edited_folder, 0755, true)) {
            throw new Exception('فشل في إنشاء مجلد النسخ المحررة');
        }
    }
    
    // إنشاء اسم ملف للنسخة المحررة
    $timestamp = date('Y-m-d_H-i-s');
    $original_name = pathinfo($document['filename'], PATHINFO_FILENAME);
    $edited_filename = "{$original_name}_edited_{$timestamp}.{$format}";
    $edited_file_path = $edited_folder . '/' . $edited_filename;
    
    // حفظ الصورة المحررة
    if (!file_put_contents($edited_file_path, $image_binary)) {
        throw new Exception('فشل في حفظ الصورة المحررة');
    }
    
    // بدء المعاملة
    $db->beginTransaction();
    
    try {
        // إذا كان المطلوب الاحتفاظ بالنسخة الأصلية، إنشاء سجل جديد
        if ($keep_original) {
            // نسخ احتياطية من النسخة الأصلية إذا لم تكن موجودة
            $backup_folder = $employee_folder . '/backup';
            if (!is_dir($backup_folder)) {
                mkdir($backup_folder, 0755, true);
            }
            
            $original_file_path = UPLOAD_PATH . $document['file_path'];
            $backup_filename = "backup_{$timestamp}_{$document['filename']}";
            $backup_file_path = $backup_folder . '/' . $backup_filename;
            
            if (file_exists($original_file_path)) {
                copy($original_file_path, $backup_file_path);
            }
            
            // إنشاء سجل جديد للنسخة المحررة
            $new_document_data = [
                'employee_id' => $document['employee_id'],
                'document_type_id' => $document['document_type_id'],
                'filename' => $edited_filename,
                'original_filename' => $document['original_filename'] . ' (محررة)',
                'file_path' => str_replace(UPLOAD_PATH, '', $edited_file_path),
                'file_size' => filesize($edited_file_path),
                'file_type' => "image/{$format}",
                'file_hash' => hash_file('sha256', $edited_file_path),
                'upload_date' => date('Y-m-d H:i:s'),
                'uploaded_by' => $_SESSION['user_id'],
                'status' => 'approved',
                'is_scanned' => $document['is_scanned'],
                'is_edited' => 1,
                'parent_document_id' => $document_id,
                'metadata' => json_encode([
                    'editing_info' => [
                        'edited_by' => $_SESSION['user_id'],
                        'edit_date' => date('Y-m-d H:i:s'),
                        'original_document_id' => $document_id,
                        'format' => $format,
                        'quality' => $quality,
                        'backup_file' => str_replace(UPLOAD_PATH, '', $backup_file_path)
                    ]
                ], JSON_UNESCAPED_UNICODE)
            ];
            
            $new_document_id = $db->insert('documents', $new_document_data);
            
            if (!$new_document_id) {
                throw new Exception('فشل في إنشاء سجل الوثيقة المحررة');
            }
            
            $result_message = 'تم حفظ النسخة المحررة كوثيقة جديدة مع الاحتفاظ بالأصلية';
            $result_document_id = $new_document_id;
            
        } else {
            // استبدال الوثيقة الأصلية
            
            // نسخ احتياطية من الأصلية
            $original_file_path = UPLOAD_PATH . $document['file_path'];
            $backup_folder = $employee_folder . '/backup';
            
            if (!is_dir($backup_folder)) {
                mkdir($backup_folder, 0755, true);
            }
            
            if (file_exists($original_file_path)) {
                $backup_filename = "original_{$timestamp}_{$document['filename']}";
                $backup_file_path = $backup_folder . '/' . $backup_filename;
                copy($original_file_path, $backup_file_path);
                
                // حذف الملف الأصلي
                unlink($original_file_path);
            }
            
            // نقل الملف المحرر إلى مكان الأصلي
            $new_original_path = dirname($original_file_path) . '/' . $edited_filename;
            rename($edited_file_path, $new_original_path);
            
            // تحديث سجل الوثيقة
            $update_data = [
                'filename' => $edited_filename,
                'file_path' => str_replace(UPLOAD_PATH, '', $new_original_path),
                'file_size' => filesize($new_original_path),
                'file_type' => "image/{$format}",
                'file_hash' => hash_file('sha256', $new_original_path),
                'is_edited' => 1,
                'metadata' => json_encode([
                    'editing_info' => [
                        'edited_by' => $_SESSION['user_id'],
                        'edit_date' => date('Y-m-d H:i:s'),
                        'format' => $format,
                        'quality' => $quality,
                        'backup_file' => str_replace(UPLOAD_PATH, '', $backup_file_path)
                    ],
                    'original_metadata' => json_decode($document['metadata'], true)
                ], JSON_UNESCAPED_UNICODE)
            ];
            
            $updated = $db->update('documents', $update_data, ['id' => $document_id]);
            
            if (!$updated) {
                throw new Exception('فشل في تحديث سجل الوثيقة');
            }
            
            $result_message = 'تم حفظ التعديلات على الوثيقة الأصلية';
            $result_document_id = $document_id;
        }
        
        // تسجيل العملية في سجل المراجعة
        logActivity(
            'document_edited',
            'documents',
            $result_document_id,
            [
                'employee_id' => $document['employee_id'],
                'employee_name' => $document['nom'] . ' ' . $document['prenom'],
                'employee_matricule' => $document['matricule'],
                'document_type' => $document['type_name_arabe'],
                'original_filename' => $document['filename'],
                'edited_filename' => $edited_filename,
                'format' => $format,
                'quality' => $quality,
                'keep_original' => $keep_original,
                'file_size' => formatFileSize(filesize($keep_original ? $edited_file_path : $new_original_path))
            ]
        );
        
        // تأكيد المعاملة
        $db->commit();
        
        // إنشاء صورة مصغرة للنسخة المحررة
        $thumbnail_path = null;
        if ($format !== 'pdf') {
            $thumbnail_path = createThumbnail(
                $keep_original ? $edited_file_path : $new_original_path,
                dirname($keep_original ? $edited_file_path : $new_original_path)
            );
        }
        
        // إرسال إشعار (إذا كان مفعلاً)
        if (NOTIFICATIONS_ENABLED) {
            sendNotification([
                'type' => 'document_edited',
                'title' => 'تم تحرير وثيقة',
                'message' => "تم تحرير وثيقة {$document['type_name_arabe']} للموظف {$document['nom']} {$document['prenom']}",
                'user_id' => $_SESSION['user_id'],
                'related_id' => $result_document_id,
                'related_type' => 'document'
            ]);
        }
        
        // إرجاع النتيجة
        echo json_encode([
            'success' => true,
            'message' => $result_message,
            'data' => [
                'document_id' => $result_document_id,
                'filename' => $edited_filename,
                'file_size' => formatFileSize(filesize($keep_original ? $edited_file_path : $new_original_path)),
                'thumbnail' => $thumbnail_path,
                'employee_name' => $document['nom'] . ' ' . $document['prenom'],
                'document_type' => $document['type_name_arabe'],
                'edit_info' => [
                    'format' => $format,
                    'quality' => $quality,
                    'keep_original' => $keep_original,
                    'edit_date' => date('Y-m-d H:i:s')
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        // التراجع عن المعاملة
        $db->rollback();
        
        // حذف الملف المحرر في حالة الفشل
        if (file_exists($edited_file_path)) {
            unlink($edited_file_path);
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("خطأ في حفظ الوثيقة المحررة: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * التحقق من صلاحية تحرير الوثيقة
 */
function canEditDocument($document) {
    // المدير يمكنه تحرير جميع الوثائق
    if (hasRole('admin')) {
        return true;
    }
    
    // مسؤول الموارد البشرية يمكنه تحرير جميع الوثائق
    if (hasRole('hr_manager')) {
        return true;
    }
    
    // موظف الموارد البشرية يمكنه تحرير الوثائق التي رفعها
    if (hasRole('hr_employee')) {
        return $document['uploaded_by'] == $_SESSION['user_id'];
    }
    
    // مسؤول الأرشيف يمكنه تحرير الوثائق التي رفعها
    if (hasRole('archivist')) {
        return $document['uploaded_by'] == $_SESSION['user_id'];
    }
    
    return false;
}

/**
 * إنشاء صورة مصغرة
 */
function createThumbnail($source_path, $destination_folder) {
    try {
        $thumbnail_folder = $destination_folder . '/thumbnails';
        if (!is_dir($thumbnail_folder)) {
            mkdir($thumbnail_folder, 0755, true);
        }
        
        $filename = basename($source_path);
        $thumbnail_path = $thumbnail_folder . '/thumb_' . $filename;
        
        // قراءة الصورة الأصلية
        $image_info = getimagesize($source_path);
        if (!$image_info) {
            return null;
        }
        
        $mime_type = $image_info['mime'];
        
        switch ($mime_type) {
            case 'image/jpeg':
                $source_image = imagecreatefromjpeg($source_path);
                break;
            case 'image/png':
                $source_image = imagecreatefrompng($source_path);
                break;
            default:
                return null;
        }
        
        if (!$source_image) {
            return null;
        }
        
        // حساب الأبعاد الجديدة
        $original_width = imagesx($source_image);
        $original_height = imagesy($source_image);
        
        $max_size = 200;
        if ($original_width > $original_height) {
            $new_width = $max_size;
            $new_height = ($original_height * $max_size) / $original_width;
        } else {
            $new_height = $max_size;
            $new_width = ($original_width * $max_size) / $original_height;
        }
        
        // إنشاء الصورة المصغرة
        $thumbnail = imagecreatetruecolor($new_width, $new_height);
        
        // للصور PNG، الحفاظ على الشفافية
        if ($mime_type === 'image/png') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
        }
        
        imagecopyresampled(
            $thumbnail, $source_image,
            0, 0, 0, 0,
            $new_width, $new_height,
            $original_width, $original_height
        );
        
        // حفظ الصورة المصغرة
        $success = false;
        switch ($mime_type) {
            case 'image/jpeg':
                $success = imagejpeg($thumbnail, $thumbnail_path, 85);
                break;
            case 'image/png':
                $success = imagepng($thumbnail, $thumbnail_path, 8);
                break;
        }
        
        // تنظيف الذاكرة
        imagedestroy($source_image);
        imagedestroy($thumbnail);
        
        return $success ? str_replace(UPLOAD_PATH, '', $thumbnail_path) : null;
        
    } catch (Exception $e) {
        error_log("خطأ في إنشاء الصورة المصغرة: " . $e->getMessage());
        return null;
    }
}

/**
 * إرسال إشعار
 */
function sendNotification($notification_data) {
    try {
        // يمكن إضافة منطق الإشعارات هنا
        return true;
    } catch (Exception $e) {
        error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
        return false;
    }
}
?>

<?php
/**
 * لوحة التحكم الرئيسية
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$page_title = 'لوحة التحكم الرئيسية';

// جلب الإحصائيات العامة
$stats = [];

// إحصائيات الموظفين
$stats['employees'] = [
    'total' => $db->fetchValue("SELECT COUNT(*) FROM employees"),
    'active' => $db->fetchValue("SELECT COUNT(*) FROM employees WHERE status = 'active'"),
    'new_this_month' => $db->fetchValue("
        SELECT COUNT(*) FROM employees 
        WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
    ")
];

// إحصائيات الوثائق
$stats['documents'] = [
    'total' => $db->fetchValue("SELECT COUNT(*) FROM documents WHERE status != 'deleted'"),
    'pending' => $db->fetchValue("SELECT COUNT(*) FROM documents WHERE status = 'pending'"),
    'approved' => $db->fetchValue("SELECT COUNT(*) FROM documents WHERE status = 'approved'"),
    'scanned' => $db->fetchValue("SELECT COUNT(*) FROM documents WHERE is_scanned = 1"),
    'edited' => $db->fetchValue("SELECT COUNT(*) FROM documents WHERE is_edited = 1"),
    'uploaded_today' => $db->fetchValue("
        SELECT COUNT(*) FROM documents 
        WHERE DATE(upload_date) = CURDATE() AND status != 'deleted'
    "),
    'total_size' => $db->fetchValue("SELECT SUM(file_size) FROM documents WHERE status != 'deleted'")
];

// إحصائيات أنواع الوثائق
$document_types_stats = $db->fetchAll("
    SELECT dt.type_name_arabe, dt.category, COUNT(d.id) as count
    FROM document_types dt
    LEFT JOIN documents d ON dt.id = d.document_type_id AND d.status != 'deleted'
    GROUP BY dt.id, dt.type_name_arabe, dt.category
    ORDER BY count DESC
    LIMIT 10
");

// إحصائيات المستخدمين
$stats['users'] = [
    'total' => $db->fetchValue("SELECT COUNT(*) FROM users WHERE status = 'active'"),
    'online' => $db->fetchValue("
        SELECT COUNT(DISTINCT user_id) FROM sessions 
        WHERE last_activity > UNIX_TIMESTAMP() - 300
    "),
    'admins' => $db->fetchValue("SELECT COUNT(*) FROM users WHERE role = 'admin' AND status = 'active'"),
    'hr_managers' => $db->fetchValue("SELECT COUNT(*) FROM users WHERE role = 'hr_manager' AND status = 'active'")
];

// النشاطات الأخيرة
$recent_activities = $db->fetchAll("
    SELECT al.*, u.username, u.full_name
    FROM audit_log al
    LEFT JOIN users u ON al.user_id = u.id
    ORDER BY al.created_at DESC
    LIMIT 10
");

// الوثائق المرفوعة حديثاً
$recent_documents = $db->fetchAll("
    SELECT d.*, dt.type_name_arabe, e.nom, e.prenom, e.matricule, u.full_name as uploaded_by_name
    FROM documents d
    JOIN document_types dt ON d.document_type_id = dt.id
    JOIN employees e ON d.employee_id = e.id
    LEFT JOIN users u ON d.uploaded_by = u.id
    WHERE d.status != 'deleted'
    ORDER BY d.upload_date DESC
    LIMIT 8
");

// إحصائيات الأداء الشهرية
$monthly_stats = $db->fetchAll("
    SELECT 
        DATE_FORMAT(upload_date, '%Y-%m') as month,
        COUNT(*) as documents_count,
        SUM(file_size) as total_size
    FROM documents 
    WHERE status != 'deleted' 
        AND upload_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(upload_date, '%Y-%m')
    ORDER BY month DESC
    LIMIT 12
");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام إدارة الوثائق</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .stats-card.employees { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stats-card.documents { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-card.users { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stats-card.storage { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .stats-change {
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        
        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }
        
        .activity-item:hover {
            background: #f8f9fa;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
        }
        
        .activity-icon.upload { background: #e3f2fd; color: #1976d2; }
        .activity-icon.edit { background: #fff3e0; color: #f57c00; }
        .activity-icon.delete { background: #ffebee; color: #d32f2f; }
        .activity-icon.login { background: #e8f5e8; color: #388e3c; }
        
        .document-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .document-card:hover {
            transform: translateY(-3px);
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .quick-action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-speedometer2"></i> لوحة التحكم
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../includes/logout.php"><i class="bi bi-box-arrow-right"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- الإحصائيات الرئيسية -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card employees">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo number_format($stats['employees']['total']); ?></div>
                            <div class="stats-label">إجمالي الموظفين</div>
                            <div class="stats-change">
                                <i class="bi bi-arrow-up"></i> 
                                <?php echo number_format($stats['employees']['new_this_month']); ?> جديد هذا الشهر
                            </div>
                        </div>
                        <div>
                            <i class="bi bi-people" style="font-size: 3rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card documents">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo number_format($stats['documents']['total']); ?></div>
                            <div class="stats-label">إجمالي الوثائق</div>
                            <div class="stats-change">
                                <i class="bi bi-arrow-up"></i> 
                                <?php echo number_format($stats['documents']['uploaded_today']); ?> اليوم
                            </div>
                        </div>
                        <div>
                            <i class="bi bi-file-earmark-text" style="font-size: 3rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card users">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo number_format($stats['users']['total']); ?></div>
                            <div class="stats-label">المستخدمين النشطين</div>
                            <div class="stats-change">
                                <i class="bi bi-circle-fill text-success" style="font-size: 0.5rem;"></i> 
                                <?php echo number_format($stats['users']['online']); ?> متصل الآن
                            </div>
                        </div>
                        <div>
                            <i class="bi bi-person-check" style="font-size: 3rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card storage">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo formatFileSize($stats['documents']['total_size']); ?></div>
                            <div class="stats-label">مساحة التخزين</div>
                            <div class="stats-change">
                                <i class="bi bi-hdd"></i> 
                                <?php echo number_format($stats['documents']['scanned']); ?> ممسوحة ضوئياً
                            </div>
                        </div>
                        <div>
                            <i class="bi bi-hdd-stack" style="font-size: 3rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسوم البيانية -->
            <div class="col-md-8">
                <!-- رسم بياني للوثائق الشهرية -->
                <div class="chart-container">
                    <h5><i class="bi bi-bar-chart"></i> إحصائيات الوثائق الشهرية</h5>
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
                
                <!-- رسم بياني لأنواع الوثائق -->
                <div class="chart-container">
                    <h5><i class="bi bi-pie-chart"></i> توزيع أنواع الوثائق</h5>
                    <canvas id="documentTypesChart" height="100"></canvas>
                </div>
            </div>
            
            <!-- الشريط الجانبي -->
            <div class="col-md-4">
                <!-- الإجراءات السريعة -->
                <div class="chart-container">
                    <h5><i class="bi bi-lightning"></i> الإجراءات السريعة</h5>
                    <div class="row">
                        <div class="col-6">
                            <a href="document_scanner.php" class="quick-action-btn">
                                <i class="bi bi-scanner"></i><br>
                                مسح وثيقة
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="employees.php" class="quick-action-btn">
                                <i class="bi bi-person-plus"></i><br>
                                إضافة موظف
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="search.php" class="quick-action-btn">
                                <i class="bi bi-search"></i><br>
                                البحث المتقدم
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="reports.php" class="quick-action-btn">
                                <i class="bi bi-file-earmark-bar-graph"></i><br>
                                التقارير
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- النشاطات الأخيرة -->
                <div class="chart-container">
                    <h5><i class="bi bi-clock-history"></i> النشاطات الأخيرة</h5>
                    <div class="activity-list" style="max-height: 300px; overflow-y: auto;">
                        <?php foreach ($recent_activities as $activity): ?>
                        <div class="activity-item d-flex align-items-center">
                            <div class="activity-icon <?php echo getActivityIconClass($activity['action']); ?>">
                                <i class="bi <?php echo getActivityIcon($activity['action']); ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold"><?php echo getActivityDescription($activity); ?></div>
                                <small class="text-muted">
                                    <?php echo $activity['full_name'] ?? 'مستخدم محذوف'; ?> - 
                                    <?php echo timeAgo($activity['created_at']); ?>
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الوثائق المرفوعة حديثاً -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5><i class="bi bi-file-earmark-plus"></i> الوثائق المرفوعة حديثاً</h5>
                    <div class="row">
                        <?php foreach ($recent_documents as $doc): ?>
                        <div class="col-md-3">
                            <div class="document-card card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0"><?php echo htmlspecialchars($doc['type_name_arabe']); ?></h6>
                                        <span class="badge bg-<?php echo getStatusColor($doc['status']); ?>">
                                            <?php echo getStatusText($doc['status']); ?>
                                        </span>
                                    </div>
                                    <p class="card-text">
                                        <strong><?php echo htmlspecialchars($doc['nom'] . ' ' . $doc['prenom']); ?></strong><br>
                                        <small class="text-muted">رقم التسجيل: <?php echo htmlspecialchars($doc['matricule']); ?></small>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted"><?php echo timeAgo($doc['upload_date']); ?></small>
                                        <div>
                                            <?php if ($doc['is_scanned']): ?>
                                                <i class="bi bi-scanner text-primary" title="ممسوحة ضوئياً"></i>
                                            <?php endif; ?>
                                            <?php if ($doc['is_edited']): ?>
                                                <i class="bi bi-pencil text-warning" title="محررة"></i>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // رسم بياني للوثائق الشهرية
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_reverse(array_column($monthly_stats, 'month'))) . "'"; ?>],
                datasets: [{
                    label: 'عدد الوثائق',
                    data: [<?php echo implode(',', array_reverse(array_column($monthly_stats, 'documents_count'))); ?>],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // رسم بياني لأنواع الوثائق
        const typesCtx = document.getElementById('documentTypesChart').getContext('2d');
        const documentTypesChart = new Chart(typesCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($document_types_stats, 'type_name_arabe')) . "'"; ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_column($document_types_stats, 'count')); ?>],
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            // يمكن إضافة AJAX لتحديث الإحصائيات
        }, 30000);
    </script>
</body>
</html>

<?php
// دوال مساعدة
function getActivityIconClass($action) {
    switch ($action) {
        case 'document_uploaded': return 'upload';
        case 'document_edited': return 'edit';
        case 'document_deleted': return 'delete';
        case 'user_login': return 'login';
        default: return 'upload';
    }
}

function getActivityIcon($action) {
    switch ($action) {
        case 'document_uploaded': return 'bi-upload';
        case 'document_edited': return 'bi-pencil';
        case 'document_deleted': return 'bi-trash';
        case 'user_login': return 'bi-box-arrow-in-right';
        default: return 'bi-activity';
    }
}

function getActivityDescription($activity) {
    switch ($activity['action']) {
        case 'document_uploaded': return 'تم رفع وثيقة جديدة';
        case 'document_edited': return 'تم تحرير وثيقة';
        case 'document_deleted': return 'تم حذف وثيقة';
        case 'user_login': return 'تسجيل دخول';
        default: return $activity['action'];
    }
}

function getStatusColor($status) {
    switch ($status) {
        case 'approved': return 'success';
        case 'pending': return 'warning';
        case 'rejected': return 'danger';
        default: return 'secondary';
    }
}

function getStatusText($status) {
    switch ($status) {
        case 'approved': return 'معتمدة';
        case 'pending': return 'في الانتظار';
        case 'rejected': return 'مرفوضة';
        default: return 'غير محدد';
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    
    return date('Y-m-d', strtotime($datetime));
}
?>

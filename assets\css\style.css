/**
 * ملف التصميم الرئيسي
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

/* إعدادات عامة */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* الخط العربي */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f5f6fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* تحسين النصوص العربية */
.arabic-text {
    font-family: '<PERSON><PERSON>', 'Times New Roman', serif;
    font-size: 1.1em;
    line-height: 1.8;
}

/* الحاوي الرئيسي */
.main-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    box-shadow: var(--box-shadow);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin: 0 0.2rem;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 2rem;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.6rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #5dade2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #2980b9, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #58d68d);
}

.btn-success:hover {
    background: linear-gradient(45deg, #229954, var(--success-color));
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #f7dc6f);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #ec7063);
}

.btn-sm {
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* حقول الإدخال */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(45deg, rgba(39, 174, 96, 0.1), rgba(88, 214, 141, 0.1));
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(236, 112, 99, 0.1));
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(45deg, rgba(243, 156, 18, 0.1), rgba(247, 220, 111, 0.1));
    color: var(--warning-color);
    border-right: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(45deg, rgba(23, 162, 184, 0.1), rgba(93, 173, 226, 0.1));
    color: var(--info-color);
    border-right: 4px solid var(--info-color);
}

/* الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
}

/* الصور */
.employee-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
    transition: var(--transition);
}

.employee-photo:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.employee-photo-large {
    width: 150px;
    height: 150px;
    border-radius: 15px;
    object-fit: cover;
    border: 4px solid var(--primary-color);
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* منطقة السحب والإفلات */
.drop-zone {
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    background: rgba(52, 152, 219, 0.05);
    transition: var(--transition);
    cursor: pointer;
}

.drop-zone:hover,
.drop-zone.dragover {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--secondary-color);
}

.drop-zone i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* التصفح */
.pagination .page-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    border-radius: var(--border-radius);
    margin: 0 0.2rem;
    transition: var(--transition);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.stats-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* البحث */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 3rem;
}

.search-box .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    z-index: 10;
}

/* الفلاتر */
.filter-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

/* التوافق مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .employee-photo-large {
        width: 100px;
        height: 100px;
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين الطباعة */
@media print {
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

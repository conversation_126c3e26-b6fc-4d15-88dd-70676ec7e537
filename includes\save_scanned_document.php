<?php
/**
 * حفظ الوثائق الممسوحة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('add_document')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذه العملية']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

try {
    $db = Database::getInstance();
    
    // التحقق من البيانات المطلوبة
    $required_fields = ['employee_id', 'document_type_id'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("الحقل $field مطلوب");
        }
    }
    
    $employee_id = (int)$_POST['employee_id'];
    $document_type_id = (int)$_POST['document_type_id'];
    $source = $_POST['source'] ?? 'unknown';
    $resolution = (int)($_POST['resolution'] ?? 300);
    $color_mode = $_POST['color_mode'] ?? 'color';
    
    // التحقق من وجود الموظف
    $employee = $db->fetchOne("SELECT id, nom, prenom FROM employees WHERE id = ?", [$employee_id]);
    if (!$employee) {
        throw new Exception('الموظف غير موجود');
    }
    
    // التحقق من نوع الوثيقة
    $document_type = $db->fetchOne("SELECT * FROM document_types WHERE id = ?", [$document_type_id]);
    if (!$document_type) {
        throw new Exception('نوع الوثيقة غير صحيح');
    }
    
    // التحقق من وجود الملف المرفوع
    if (!isset($_FILES['document']) || $_FILES['document']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('لم يتم رفع الملف أو حدث خطأ في الرفع');
    }
    
    $uploaded_file = $_FILES['document'];
    
    // التحقق من نوع الملف
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    $file_type = $uploaded_file['type'];
    
    if (!in_array($file_type, $allowed_types)) {
        throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, PDF');
    }
    
    // التحقق من حجم الملف (10MB كحد أقصى)
    $max_size = 10 * 1024 * 1024; // 10MB
    if ($uploaded_file['size'] > $max_size) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 10MB');
    }
    
    // إنشاء مجلد الموظف إذا لم يكن موجوداً
    $employee_folder = UPLOAD_PATH . '/documents/' . $employee_id;
    if (!is_dir($employee_folder)) {
        if (!mkdir($employee_folder, 0755, true)) {
            throw new Exception('فشل في إنشاء مجلد الموظف');
        }
    }
    
    // إنشاء اسم ملف فريد
    $file_extension = pathinfo($uploaded_file['name'], PATHINFO_EXTENSION);
    if (empty($file_extension)) {
        // تحديد الامتداد بناءً على نوع MIME
        switch ($file_type) {
            case 'image/jpeg':
            case 'image/jpg':
                $file_extension = 'jpg';
                break;
            case 'image/png':
                $file_extension = 'png';
                break;
            case 'application/pdf':
                $file_extension = 'pdf';
                break;
            default:
                $file_extension = 'jpg';
        }
    }
    
    $timestamp = date('Y-m-d_H-i-s');
    $unique_id = uniqid();
    $filename = "scanned_{$timestamp}_{$unique_id}.{$file_extension}";
    $file_path = $employee_folder . '/' . $filename;
    
    // نقل الملف المرفوع
    if (!move_uploaded_file($uploaded_file['tmp_name'], $file_path)) {
        throw new Exception('فشل في حفظ الملف');
    }
    
    // حساب hash الملف للتحقق من التكرار
    $file_hash = hash_file('sha256', $file_path);
    
    // التحقق من عدم وجود ملف مطابق
    $existing_doc = $db->fetchOne(
        "SELECT id FROM documents WHERE employee_id = ? AND file_hash = ?",
        [$employee_id, $file_hash]
    );
    
    if ($existing_doc) {
        unlink($file_path); // حذف الملف المكرر
        throw new Exception('هذه الوثيقة موجودة بالفعل');
    }
    
    // معلومات إضافية عن الملف
    $file_info = [
        'source' => $source,
        'resolution' => $resolution,
        'color_mode' => $color_mode,
        'scan_date' => date('Y-m-d H:i:s'),
        'scanner_info' => $_POST['scanner_info'] ?? null
    ];
    
    // إدراج الوثيقة في قاعدة البيانات
    $document_data = [
        'employee_id' => $employee_id,
        'document_type_id' => $document_type_id,
        'filename' => $filename,
        'original_filename' => $uploaded_file['name'],
        'file_path' => str_replace(UPLOAD_PATH, '', $file_path),
        'file_size' => $uploaded_file['size'],
        'file_type' => $file_type,
        'file_hash' => $file_hash,
        'upload_date' => date('Y-m-d H:i:s'),
        'uploaded_by' => $_SESSION['user_id'],
        'status' => 'pending_review',
        'metadata' => json_encode($file_info, JSON_UNESCAPED_UNICODE),
        'is_scanned' => 1
    ];
    
    $document_id = $db->insert('documents', $document_data);
    
    if (!$document_id) {
        unlink($file_path); // حذف الملف في حالة فشل الإدراج
        throw new Exception('فشل في حفظ معلومات الوثيقة');
    }
    
    // تسجيل العملية في سجل المراجعة
    logActivity(
        'document_scanned',
        'documents',
        $document_id,
        [
            'employee_id' => $employee_id,
            'employee_name' => $employee['nom'] . ' ' . $employee['prenom'],
            'document_type' => $document_type['type_name_arabe'],
            'filename' => $filename,
            'source' => $source,
            'file_size' => formatFileSize($uploaded_file['size'])
        ]
    );
    
    // إنشاء صورة مصغرة للصور
    $thumbnail_path = null;
    if (in_array($file_type, ['image/jpeg', 'image/jpg', 'image/png'])) {
        $thumbnail_path = createThumbnail($file_path, $employee_folder);
    }
    
    // إرسال إشعار (إذا كان مفعلاً)
    if (NOTIFICATIONS_ENABLED) {
        sendNotification([
            'type' => 'document_scanned',
            'title' => 'تم مسح وثيقة جديدة',
            'message' => "تم مسح وثيقة {$document_type['type_name_arabe']} للموظف {$employee['nom']} {$employee['prenom']}",
            'user_id' => $_SESSION['user_id'],
            'related_id' => $document_id,
            'related_type' => 'document'
        ]);
    }
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ الوثيقة الممسوحة بنجاح',
        'data' => [
            'document_id' => $document_id,
            'filename' => $filename,
            'file_size' => formatFileSize($uploaded_file['size']),
            'thumbnail' => $thumbnail_path,
            'employee_name' => $employee['nom'] . ' ' . $employee['prenom'],
            'document_type' => $document_type['type_name_arabe'],
            'scan_info' => $file_info
        ]
    ]);
    
} catch (Exception $e) {
    error_log("خطأ في حفظ الوثيقة الممسوحة: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * إنشاء صورة مصغرة
 */
function createThumbnail($source_path, $destination_folder) {
    try {
        $thumbnail_folder = $destination_folder . '/thumbnails';
        if (!is_dir($thumbnail_folder)) {
            mkdir($thumbnail_folder, 0755, true);
        }
        
        $filename = basename($source_path);
        $thumbnail_path = $thumbnail_folder . '/thumb_' . $filename;
        
        // قراءة الصورة الأصلية
        $image_info = getimagesize($source_path);
        $mime_type = $image_info['mime'];
        
        switch ($mime_type) {
            case 'image/jpeg':
                $source_image = imagecreatefromjpeg($source_path);
                break;
            case 'image/png':
                $source_image = imagecreatefrompng($source_path);
                break;
            default:
                return null;
        }
        
        if (!$source_image) {
            return null;
        }
        
        // حساب الأبعاد الجديدة (200x200 كحد أقصى)
        $original_width = imagesx($source_image);
        $original_height = imagesy($source_image);
        
        $max_size = 200;
        if ($original_width > $original_height) {
            $new_width = $max_size;
            $new_height = ($original_height * $max_size) / $original_width;
        } else {
            $new_height = $max_size;
            $new_width = ($original_width * $max_size) / $original_height;
        }
        
        // إنشاء الصورة المصغرة
        $thumbnail = imagecreatetruecolor($new_width, $new_height);
        
        // للصور PNG، الحفاظ على الشفافية
        if ($mime_type === 'image/png') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
        }
        
        imagecopyresampled(
            $thumbnail, $source_image,
            0, 0, 0, 0,
            $new_width, $new_height,
            $original_width, $original_height
        );
        
        // حفظ الصورة المصغرة
        $success = false;
        switch ($mime_type) {
            case 'image/jpeg':
                $success = imagejpeg($thumbnail, $thumbnail_path, 85);
                break;
            case 'image/png':
                $success = imagepng($thumbnail, $thumbnail_path, 8);
                break;
        }
        
        // تنظيف الذاكرة
        imagedestroy($source_image);
        imagedestroy($thumbnail);
        
        return $success ? str_replace(UPLOAD_PATH, '', $thumbnail_path) : null;
        
    } catch (Exception $e) {
        error_log("خطأ في إنشاء الصورة المصغرة: " . $e->getMessage());
        return null;
    }
}

/**
 * إرسال إشعار
 */
function sendNotification($notification_data) {
    try {
        $db = Database::getInstance();
        
        // يمكن إضافة منطق الإشعارات هنا
        // مثل إرسال بريد إلكتروني أو إشعار في النظام
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في إرسال الإشعار: " . $e->getMessage());
        return false;
    }
}
?>

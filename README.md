# نظام إدارة الوثائق الإلكترونية للموظفين

## 📋 وصف المشروع

نظام رقمي متكامل لإدارة وأرشفة الوثائق الخاصة بموظفي **direction des oeuvres universitaires msila** يضم أكثر من 1,000 موظف.

### ✨ الميزات الرئيسية

- 📁 إدارة شاملة لملفات الموظفين
- 📄 رفع وتصنيف الوثائق (PDF, JPG, PNG)
- 🔍 بحث متقدم وفلاتر ذكية
- 👥 نظام صلاحيات متعدد المستويات
- 📊 تقارير وإحصائيات
- 🔒 أمان عالي وتشفير البيانات
- 🌐 واجهة متعددة اللغات (عربي، فرنسي، إنجليزي)

## 🚀 متطلبات التشغيل

### البرامج المطلوبة
- **XAMPP** (Apache + MySQL + PHP 8.0+)
- **Python 3.x** مع مكتبة pandas (لاستيراد Excel)
- متصفح ويب حديث

### متطلبات الخادم
- PHP 8.0 أو أحدث
- MySQL 8.0 أو MariaDB 10.4+
- مساحة تخزين: 10GB+ (للوثائق)
- ذاكرة: 2GB RAM كحد أدنى

## 📦 التثبيت والإعداد

### 1. تشغيل XAMPP
```bash
# تشغيل XAMPP Control Panel
C:\xampp\xampp-control.exe

# تشغيل Apache و MySQL من لوحة التحكم
```

### 2. إنشاء قاعدة البيانات
```sql
-- تشغيل الأوامر التالية في phpMyAdmin أو MySQL Command Line

-- 1. إنشاء قاعدة البيانات
source sql/01_create_database.sql

-- 2. إنشاء الجداول
source sql/02_create_tables.sql

-- 3. إدراج أنواع الوثائق والمستخدمين الافتراضيين
source sql/03_insert_document_types.sql
```

### 3. تثبيت Python ومكتباته (لاستيراد Excel)
```bash
# تثبيت pandas
pip install pandas openpyxl

# أو باستخدام conda
conda install pandas openpyxl
```

### 4. إعداد الصلاحيات
```bash
# إنشاء مجلدات التحميل
mkdir uploads
mkdir uploads/documents
mkdir uploads/photos
mkdir uploads/temp

# تعيين صلاحيات الكتابة (Linux/Mac)
chmod 755 uploads/
chmod 755 uploads/documents/
chmod 755 uploads/photos/
chmod 755 uploads/temp/
```

### 5. استيراد بيانات الموظفين الموجودة
```php
// زيارة الرابط التالي لاستيراد البيانات
http://localhost/Edocs/includes/import_excel.php
```

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
```php
// ملف: config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'dossier_employes');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### إعدادات رفع الملفات
```php
// ملف: config/config.php
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 MB
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx']);
```

## 👤 الحسابات الافتراضية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| مدير النظام | `admin` | `password` | جميع الصلاحيات |
| مسؤول الموارد البشرية | `hr_manager` | `password` | إدارة الموظفين والوثائق |
| مسؤول الأرشيف | `archivist` | `password` | عرض وإضافة الوثائق |

⚠️ **مهم**: يجب تغيير كلمات المرور الافتراضية فوراً!

## 🌐 الوصول للنظام

### الروابط الرئيسية
- **الصفحة الرئيسية**: `http://localhost/Edocs/`
- **تسجيل الدخول**: `http://localhost/Edocs/pages/login.php`
- **لوحة التحكم**: `http://localhost/Edocs/pages/dashboard.php`

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية
1. **employees** - بيانات الموظفين (1,062 موظف)
2. **documents** - الوثائق المرفوعة
3. **document_types** - أنواع الوثائق (42 نوع)
4. **users** - مستخدمي النظام
5. **audit_log** - سجل العمليات
6. **sessions** - جلسات المستخدمين

### العلاقات
- موظف واحد ← عدة وثائق
- نوع وثيقة واحد ← عدة وثائق
- مستخدم واحد ← عدة عمليات

## 🔍 استخدام النظام

### إضافة موظف جديد
1. تسجيل الدخول بحساب له صلاحية
2. الذهاب إلى "إدارة الموظفين"
3. النقر على "إضافة موظف جديد"
4. ملء البيانات المطلوبة
5. رفع الصورة الشخصية (اختياري)

### رفع وثيقة
1. البحث عن الموظف
2. فتح ملف الموظف
3. النقر على "رفع وثيقة جديدة"
4. اختيار نوع الوثيقة
5. سحب الملف أو اختياره
6. إضافة المعلومات الإضافية

### البحث المتقدم
- البحث بالاسم أو رقم التسجيل
- فلترة حسب الهيكل أو الرتبة
- البحث في محتوى الوثائق
- فلترة حسب تاريخ التوظيف

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
خطأ: SQLSTATE[HY000] [2002] No connection could be made
```
**الحل**: تأكد من تشغيل MySQL في XAMPP

#### خطأ في رفع الملفات
```
خطأ: فشل في رفع الملف
```
**الحل**: تحقق من صلاحيات مجلد uploads

#### خطأ في استيراد Excel
```
خطأ: Python أو مكتبة pandas غير متوفرة
```
**الحل**: تثبيت Python و pandas

### ملفات السجلات
- **أخطاء النظام**: `logs/error.log`
- **سجل العمليات**: جدول `audit_log` في قاعدة البيانات

## 📈 الأداء والتحسين

### للمؤسسات الكبيرة (100,000+ موظف)
1. **تحسين قاعدة البيانات**:
   - استخدام فهارس مركبة
   - تقسيم الجداول (Partitioning)
   - تحسين استعلامات البحث

2. **تحسين التخزين**:
   - استخدام Amazon S3 للملفات
   - ضغط الصور تلقائياً
   - تنظيف الملفات المؤقتة

3. **تحسين الأداء**:
   - استخدام Redis للتخزين المؤقت
   - تحسين إعدادات PHP
   - استخدام CDN للملفات الثابتة

## 🔒 الأمان

### إجراءات الأمان المطبقة
- تشفير كلمات المرور (bcrypt)
- حماية من CSRF
- تسجيل جميع العمليات
- صلاحيات متدرجة
- حماية الملفات المرفوعة
- جلسات آمنة

### توصيات إضافية
- استخدام HTTPS في الإنتاج
- نسخ احتياطية دورية
- تحديث كلمات المرور بانتظام
- مراقبة سجل العمليات

## 📞 الدعم والصيانة

### للحصول على المساعدة
- مراجعة ملف السجلات
- التحقق من متطلبات النظام
- التواصل مع مدير النظام

### التحديثات المستقبلية
- إضافة تقييمات الأداء
- نظام الإجازات والغيابات
- تقارير متقدمة
- تطبيق موبايل

## 📄 الترخيص

هذا النظام مطور خصيصاً لـ **direction des oeuvres universitaires msila**

---

**الإصدار**: 1.0.0  
**تاريخ الإنشاء**: سبتمبر 2025  
**المطور**: نظام إدارة الوثائق الإلكترونية

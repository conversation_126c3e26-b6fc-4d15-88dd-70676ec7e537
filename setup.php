<?php
/**
 * ملف إعداد النظام التلقائي
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'dossier_employes';

$setup_steps = [];
$errors = [];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - نظام إدارة الوثائق الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 800px;
        }
        .setup-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .step {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .step.success {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .step.error {
            border-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
        .step.running {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }
        .btn-setup {
            background: linear-gradient(45deg, #3498db, #2c3e50);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h2><i class="bi bi-gear"></i> إعداد نظام إدارة الوثائق الإلكترونية</h2>
                <p>مرحباً بك! سنقوم بإعداد النظام خطوة بخطوة</p>
            </div>
            
            <div class="p-4">
                <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                    
                    <!-- خطوة 1: التحقق من متطلبات النظام -->
                    <div class="step" id="step1">
                        <h5><i class="bi bi-check-circle"></i> التحقق من متطلبات النظام</h5>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 20%"></div>
                        </div>
                        <?php
                        $php_version = phpversion();
                        $mysql_available = extension_loaded('pdo_mysql');
                        $uploads_writable = is_writable(dirname(__FILE__));
                        
                        if (version_compare($php_version, '8.0.0', '>=') && $mysql_available && $uploads_writable) {
                            echo '<p class="text-success"><i class="bi bi-check"></i> جميع المتطلبات متوفرة</p>';
                            echo "<small>PHP: $php_version | MySQL: متوفر | الكتابة: مسموحة</small>";
                            $setup_steps['requirements'] = true;
                        } else {
                            echo '<p class="text-danger"><i class="bi bi-x"></i> بعض المتطلبات غير متوفرة</p>';
                            if (version_compare($php_version, '8.0.0', '<')) echo "<small class='text-danger'>PHP 8.0+ مطلوب (الحالي: $php_version)</small><br>";
                            if (!$mysql_available) echo "<small class='text-danger'>امتداد MySQL غير متوفر</small><br>";
                            if (!$uploads_writable) echo "<small class='text-danger'>صلاحيات الكتابة غير متوفرة</small><br>";
                            $errors[] = 'متطلبات النظام غير مكتملة';
                        }
                        ?>
                    </div>
                    
                    <!-- خطوة 2: الاتصال بقاعدة البيانات -->
                    <div class="step" id="step2">
                        <h5><i class="bi bi-database"></i> الاتصال بقاعدة البيانات</h5>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 40%"></div>
                        </div>
                        <?php
                        try {
                            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            echo '<p class="text-success"><i class="bi bi-check"></i> تم الاتصال بخادم MySQL بنجاح</p>';
                            $setup_steps['database_connection'] = true;
                        } catch (PDOException $e) {
                            echo '<p class="text-danger"><i class="bi bi-x"></i> فشل الاتصال بقاعدة البيانات</p>';
                            echo "<small class='text-danger'>الخطأ: " . $e->getMessage() . "</small>";
                            $errors[] = 'فشل الاتصال بقاعدة البيانات';
                        }
                        ?>
                    </div>
                    
                    <!-- خطوة 3: إنشاء قاعدة البيانات -->
                    <?php if (isset($setup_steps['database_connection'])): ?>
                    <div class="step" id="step3">
                        <h5><i class="bi bi-plus-circle"></i> إنشاء قاعدة البيانات</h5>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                        <?php
                        try {
                            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                            $pdo->exec("USE `$db_name`");
                            echo '<p class="text-success"><i class="bi bi-check"></i> تم إنشاء قاعدة البيانات بنجاح</p>';
                            $setup_steps['database_created'] = true;
                        } catch (PDOException $e) {
                            echo '<p class="text-danger"><i class="bi bi-x"></i> فشل في إنشاء قاعدة البيانات</p>';
                            echo "<small class='text-danger'>الخطأ: " . $e->getMessage() . "</small>";
                            $errors[] = 'فشل في إنشاء قاعدة البيانات';
                        }
                        ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- خطوة 4: إنشاء الجداول -->
                    <?php if (isset($setup_steps['database_created'])): ?>
                    <div class="step" id="step4">
                        <h5><i class="bi bi-table"></i> إنشاء الجداول</h5>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 80%"></div>
                        </div>
                        <?php
                        try {
                            // قراءة وتنفيذ ملفات SQL
                            $sql_files = ['sql/02_create_tables.sql', 'sql/03_insert_document_types.sql'];
                            
                            foreach ($sql_files as $file) {
                                if (file_exists($file)) {
                                    $sql = file_get_contents($file);
                                    // تقسيم الاستعلامات
                                    $queries = array_filter(array_map('trim', explode(';', $sql)));
                                    
                                    foreach ($queries as $query) {
                                        if (!empty($query) && !preg_match('/^(USE|--)/i', $query)) {
                                            $pdo->exec($query);
                                        }
                                    }
                                }
                            }
                            
                            echo '<p class="text-success"><i class="bi bi-check"></i> تم إنشاء جميع الجداول بنجاح</p>';
                            echo '<small>تم إنشاء 6 جداول و 42 نوع وثيقة و 3 مستخدمين افتراضيين</small>';
                            $setup_steps['tables_created'] = true;
                        } catch (Exception $e) {
                            echo '<p class="text-danger"><i class="bi bi-x"></i> فشل في إنشاء الجداول</p>';
                            echo "<small class='text-danger'>الخطأ: " . $e->getMessage() . "</small>";
                            $errors[] = 'فشل في إنشاء الجداول';
                        }
                        ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- خطوة 5: إنشاء المجلدات -->
                    <div class="step" id="step5">
                        <h5><i class="bi bi-folder-plus"></i> إنشاء مجلدات التحميل</h5>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                        <?php
                        $directories = ['uploads', 'uploads/documents', 'uploads/photos', 'uploads/temp', 'logs'];
                        $created_dirs = 0;
                        
                        foreach ($directories as $dir) {
                            if (!is_dir($dir)) {
                                if (mkdir($dir, 0755, true)) {
                                    $created_dirs++;
                                    // إنشاء ملف .htaccess للحماية
                                    if (strpos($dir, 'uploads') !== false) {
                                        file_put_contents($dir . '/.htaccess', "Options -Indexes\nDeny from all");
                                    }
                                }
                            } else {
                                $created_dirs++;
                            }
                        }
                        
                        if ($created_dirs == count($directories)) {
                            echo '<p class="text-success"><i class="bi bi-check"></i> تم إنشاء جميع المجلدات بنجاح</p>';
                            echo '<small>تم إنشاء ' . count($directories) . ' مجلدات مع ملفات الحماية</small>';
                            $setup_steps['directories_created'] = true;
                        } else {
                            echo '<p class="text-warning"><i class="bi bi-exclamation-triangle"></i> تم إنشاء بعض المجلدات فقط</p>';
                            echo "<small>تم إنشاء $created_dirs من " . count($directories) . " مجلدات</small>";
                        }
                        ?>
                    </div>
                    
                    <!-- النتيجة النهائية -->
                    <div class="mt-4 p-3 rounded">
                        <?php if (empty($errors)): ?>
                            <div class="alert alert-success">
                                <h5><i class="bi bi-check-circle"></i> تم إعداد النظام بنجاح!</h5>
                                <p>يمكنك الآن استخدام النظام بالحسابات التالية:</p>
                                <ul>
                                    <li><strong>المدير:</strong> admin / password</li>
                                    <li><strong>مسؤول الموارد البشرية:</strong> hr_manager / password</li>
                                    <li><strong>مسؤول الأرشيف:</strong> archivist / password</li>
                                </ul>
                                <p class="text-warning"><small><i class="bi bi-exclamation-triangle"></i> يرجى تغيير كلمات المرور الافتراضية فوراً!</small></p>
                                <a href="pages/login.php" class="btn btn-success">
                                    <i class="bi bi-box-arrow-in-right"></i> الذهاب لتسجيل الدخول
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h5><i class="bi bi-x-circle"></i> فشل في إعداد النظام</h5>
                                <p>الأخطاء التي حدثت:</p>
                                <ul>
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                                <button type="button" class="btn btn-warning" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise"></i> إعادة المحاولة
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php else: ?>
                    <!-- نموذج بدء الإعداد -->
                    <div class="text-center">
                        <h4>مرحباً بك في نظام إدارة الوثائق الإلكترونية</h4>
                        <p class="text-muted">سنقوم بإعداد النظام تلقائياً في بضع خطوات</p>
                        
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> ما سيتم إنشاؤه:</h6>
                            <ul class="text-start">
                                <li>قاعدة البيانات: dossier_employes</li>
                                <li>6 جداول رئيسية</li>
                                <li>42 نوع وثيقة</li>
                                <li>3 مستخدمين افتراضيين</li>
                                <li>مجلدات التحميل والحماية</li>
                            </ul>
                        </div>
                        
                        <form method="POST">
                            <button type="submit" class="btn btn-setup btn-lg">
                                <i class="bi bi-play-circle"></i> بدء الإعداد
                            </button>
                        </form>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                تأكد من تشغيل Apache و MySQL في XAMPP قبل البدء
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث حالة الخطوات
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                const hasSuccess = step.querySelector('.text-success');
                const hasError = step.querySelector('.text-danger');
                
                if (hasSuccess) {
                    step.classList.add('success');
                } else if (hasError) {
                    step.classList.add('error');
                }
            });
        });
    </script>
</body>
</html>

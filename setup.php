<?php
/**
 * صفحة الإعداد الأولي
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'dossier_employes');

$setup_complete = false;
$errors = [];
$success_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
    try {
        // 1. الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        // 2. إنشا<PERSON> قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $success_messages[] = "تم إنشاء قاعدة البيانات بنجاح";
        
        // 3. الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        // 4. تنفيذ ملفات SQL
        $sql_files = [
            'sql/02_create_tables.sql',
            'sql/03_insert_document_types.sql'
        ];
        
        foreach ($sql_files as $file) {
            if (file_exists($file)) {
                $sql = file_get_contents($file);
                // تقسيم الاستعلامات
                $queries = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($queries as $query) {
                    if (!empty($query) && !preg_match('/^\s*--/', $query)) {
                        try {
                            $pdo->exec($query);
                        } catch (PDOException $e) {
                            // تجاهل أخطاء الجداول الموجودة
                            if (strpos($e->getMessage(), 'already exists') === false) {
                                throw $e;
                            }
                        }
                    }
                }
                $success_messages[] = "تم تنفيذ ملف: " . basename($file);
            }
        }
        
        // 5. إنشاء مجلدات التحميل
        $upload_dirs = [
            'uploads',
            'uploads/documents',
            'uploads/backups',
            'uploads/temp'
        ];
        
        foreach ($upload_dirs as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    $success_messages[] = "تم إنشاء مجلد: $dir";
                } else {
                    $errors[] = "فشل في إنشاء مجلد: $dir";
                }
            }
        }
        
        $setup_complete = true;
        
    } catch (Exception $e) {
        $errors[] = "خطأ في الإعداد: " . $e->getMessage();
    }
}

// فحص حالة النظام
$system_status = [];

// فحص PHP
$system_status['php'] = [
    'version' => PHP_VERSION,
    'status' => version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'error'
];

// فحص MySQL
try {
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $system_status['mysql'] = [
        'status' => 'success',
        'version' => $pdo->query('SELECT VERSION()')->fetchColumn()
    ];
} catch (Exception $e) {
    $system_status['mysql'] = [
        'status' => 'error',
        'message' => $e->getMessage()
    ];
}

// فحص قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $system_status['database'] = [
        'status' => count($tables) > 0 ? 'success' : 'warning',
        'tables_count' => count($tables)
    ];
} catch (Exception $e) {
    $system_status['database'] = [
        'status' => 'error',
        'message' => 'قاعدة البيانات غير موجودة'
    ];
}

// فحص مجلدات التحميل
$system_status['uploads'] = [
    'status' => is_dir('uploads') && is_writable('uploads') ? 'success' : 'error'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - نظام إدارة الوثائق الإلكترونية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .status-item {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .status-success { background: #d4edda; border-color: #c3e6cb; }
        .status-warning { background: #fff3cd; border-color: #ffeaa7; }
        .status-error { background: #f8d7da; border-color: #f5c6cb; }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .setup-header h1 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .btn-setup {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h1><i class="bi bi-gear"></i> إعداد النظام</h1>
                <p class="text-muted">نظام إدارة الوثائق الإلكترونية للموظفين</p>
            </div>
            
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h6><i class="bi bi-exclamation-triangle"></i> أخطاء:</h6>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($success_messages)): ?>
            <div class="alert alert-success">
                <h6><i class="bi bi-check-circle"></i> تم بنجاح:</h6>
                <ul class="mb-0">
                    <?php foreach ($success_messages as $message): ?>
                    <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <!-- حالة النظام -->
            <h5><i class="bi bi-clipboard-check"></i> حالة النظام</h5>
            
            <div class="status-item status-<?php echo $system_status['php']['status']; ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="bi bi-code"></i> PHP</span>
                    <span>
                        <?php if ($system_status['php']['status'] === 'success'): ?>
                            <i class="bi bi-check-circle text-success"></i> الإصدار <?php echo $system_status['php']['version']; ?>
                        <?php else: ?>
                            <i class="bi bi-x-circle text-danger"></i> يتطلب PHP 7.4+
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <div class="status-item status-<?php echo $system_status['mysql']['status']; ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="bi bi-database"></i> MySQL</span>
                    <span>
                        <?php if ($system_status['mysql']['status'] === 'success'): ?>
                            <i class="bi bi-check-circle text-success"></i> الإصدار <?php echo $system_status['mysql']['version']; ?>
                        <?php else: ?>
                            <i class="bi bi-x-circle text-danger"></i> <?php echo $system_status['mysql']['message']; ?>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <div class="status-item status-<?php echo $system_status['database']['status']; ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="bi bi-table"></i> قاعدة البيانات</span>
                    <span>
                        <?php if ($system_status['database']['status'] === 'success'): ?>
                            <i class="bi bi-check-circle text-success"></i> <?php echo $system_status['database']['tables_count']; ?> جدول
                        <?php elseif ($system_status['database']['status'] === 'warning'): ?>
                            <i class="bi bi-exclamation-triangle text-warning"></i> فارغة
                        <?php else: ?>
                            <i class="bi bi-x-circle text-danger"></i> غير موجودة
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <div class="status-item status-<?php echo $system_status['uploads']['status']; ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="bi bi-folder"></i> مجلدات التحميل</span>
                    <span>
                        <?php if ($system_status['uploads']['status'] === 'success'): ?>
                            <i class="bi bi-check-circle text-success"></i> جاهزة
                        <?php else: ?>
                            <i class="bi bi-x-circle text-danger"></i> غير موجودة
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <hr>
            
            <?php if ($setup_complete): ?>
            <div class="text-center">
                <h5 class="text-success"><i class="bi bi-check-circle"></i> تم إكمال الإعداد بنجاح!</h5>
                <p>يمكنك الآن الوصول للنظام</p>
                <a href="pages/login.php" class="btn btn-setup">
                    <i class="bi bi-box-arrow-in-right"></i> تسجيل الدخول
                </a>
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>بيانات الدخول الافتراضية:</strong><br>
                        اسم المستخدم: admin<br>
                        كلمة المرور: password
                    </small>
                </div>
            </div>
            <?php else: ?>
            <form method="POST">
                <div class="text-center">
                    <h5>بدء الإعداد</h5>
                    <p class="text-muted">سيتم إنشاء قاعدة البيانات والجداول والمجلدات المطلوبة</p>
                    
                    <button type="submit" name="setup" class="btn btn-setup">
                        <i class="bi bi-play-circle"></i> بدء الإعداد
                    </button>
                </div>
            </form>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

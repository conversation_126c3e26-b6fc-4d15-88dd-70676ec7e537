# 🎯 دليل التثبيت الشامل - نظام إدارة الوثائق الإلكترونية

## 📋 نظرة عامة على المشروع

تم إنشاء **نظام إدارة الوثائق الإلكترونية للموظفين** خصيصاً لـ **direction des oeuvres universitaires msila** لإدارة أكثر من **1,062 موظف** ووثائقهم الإدارية.

### 🎯 ما تم إنجازه:

✅ **تحليل البيانات الموجودة**: فحص ملف Excel الذي يحتوي على 1,062 موظف مع 42 حقل بيانات  
✅ **تصميم قاعدة البيانات**: إنشاء ERD متكامل مع 6 جداول رئيسية  
✅ **إنشاء ملفات SQL**: 4 ملفات SQL لإنشاء قاعدة البيانات والجداول  
✅ **هيكل المشروع**: إنشاء 15+ ملف PHP مع التصميم والحماية  
✅ **نظام الأمان**: مصادقة، صلاحيات، تشفير، وسجل العمليات  

## 🗂️ هيكل المشروع المُنشأ

```
Edocs/
├── 📁 config/
│   ├── config.php          # الإعدادات العامة
│   └── database.php        # إعدادات قاعدة البيانات
├── 📁 includes/
│   ├── auth.php            # نظام المصادقة
│   └── import_excel.php    # استيراد بيانات Excel
├── 📁 pages/
│   └── login.php           # صفحة تسجيل الدخول
├── 📁 assets/css/
│   └── style.css           # ملف التصميم الرئيسي
├── 📁 sql/
│   ├── 01_create_database.sql    # إنشاء قاعدة البيانات
│   ├── 02_create_tables.sql      # إنشاء الجداول
│   ├── 03_insert_document_types.sql  # أنواع الوثائق
│   └── 04_import_employees.sql   # استيراد الموظفين
├── 📁 uploads/              # مجلدات التحميل (ستُنشأ تلقائياً)
├── index.php               # الصفحة الرئيسية
├── setup.php               # إعداد النظام التلقائي
├── .htaccess               # ملف الحماية
├── README.md               # دليل شامل
├── QUICK_START.md          # دليل البدء السريع
└── Dossiers_Employes.xlsx  # ملف البيانات الأصلي
```

## 🚀 خطوات التثبيت التفصيلية

### المرحلة 1: إعداد البيئة

#### 1.1 تثبيت XAMPP
```bash
1. حمل XAMPP من: https://www.apachefriends.org/
2. ثبت XAMPP في C:\xampp\
3. شغل XAMPP Control Panel
4. اضغط "Start" بجانب Apache و MySQL
5. تأكد من ظهور اللون الأخضر
```

#### 1.2 تثبيت Python (لاستيراد Excel)
```bash
1. حمل Python من: https://www.python.org/downloads/
2. أثناء التثبيت، فعل "Add Python to PATH"
3. افتح Command Prompt واكتب:
   pip install pandas openpyxl
4. تأكد من التثبيت: python -c "import pandas; print('OK')"
```

### المرحلة 2: إعداد قاعدة البيانات

#### 2.1 الطريقة التلقائية (موصى بها)
```bash
1. افتح المتصفح واذهب إلى: http://localhost/Edocs/setup.php
2. اضغط "بدء الإعداد"
3. انتظر حتى اكتمال جميع الخطوات
4. ستحصل على رسالة "تم إعداد النظام بنجاح!"
```

#### 2.2 الطريقة اليدوية
```sql
-- في phpMyAdmin (http://localhost/phpmyadmin)

-- 1. إنشاء قاعدة البيانات
CREATE DATABASE dossier_employes CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. استيراد الملفات بالترتيب:
-- sql/01_create_database.sql
-- sql/02_create_tables.sql  
-- sql/03_insert_document_types.sql
```

### المرحلة 3: استيراد بيانات الموظفين

#### 3.1 التحقق من ملف Excel
```bash
# تأكد من وجود الملف في المسار الصحيح
C:\xampp\htdocs\Edocs\Dossiers_Employes.xlsx

# يجب أن يحتوي على 1,062 صف (موظف) مع الحقول التالية:
- N° (رقم تسلسلي)
- Structure (الهيكل)
- Matricule (رقم التسجيل)
- Nom, Prénom (الاسم واللقب)
- وغيرها من الحقول...
```

#### 3.2 تشغيل الاستيراد
```bash
# الطريقة 1: عبر الواجهة
1. سجل دخول بحساب admin
2. اذهب إلى لوحة التحكم
3. اضغط "استيراد بيانات الموظفين"

# الطريقة 2: مباشرة
http://localhost/Edocs/includes/import_excel.php
```

## 🔐 الحسابات الافتراضية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| **مدير النظام** | `admin` | `password` | جميع الصلاحيات |
| **مسؤول الموارد البشرية** | `hr_manager` | `password` | إدارة الموظفين والوثائق |
| **مسؤول الأرشيف** | `archivist` | `password` | عرض وإضافة الوثائق |

⚠️ **تحذير أمني**: غير كلمات المرور فوراً بعد أول تسجيل دخول!

## 📊 قاعدة البيانات المُنشأة

### الجداول الرئيسية (6 جداول):

1. **employees** (الموظفين)
   - 1,062 موظف مستورد من Excel
   - 25+ حقل شامل (شخصي، مهني، مالي)
   - فهارس محسنة للبحث السريع

2. **documents** (الوثائق)
   - ربط كل وثيقة بموظف
   - معلومات الملف (نوع، حجم، مسار)
   - تتبع التحقق والموافقة

3. **document_types** (أنواع الوثائق)
   - 42 نوع وثيقة مُعرف مسبقاً
   - مصنفة في 9 فئات رئيسية
   - قابلة للتخصيص والإضافة

4. **users** (المستخدمين)
   - 5 مستويات صلاحيات
   - تشفير bcrypt لكلمات المرور
   - تتبع محاولات تسجيل الدخول

5. **audit_log** (سجل العمليات)
   - تسجيل جميع العمليات
   - تتبع التغييرات
   - معلومات IP والمتصفح

6. **sessions** (الجلسات)
   - إدارة جلسات آمنة
   - انتهاء صلاحية تلقائي
   - تنظيف دوري

### أنواع الوثائق المُعرفة (42 نوع):

#### 🏢 وثائق التوظيف (5 أنواع)
- محضر التنصيب
- عقد العمل  
- قرار التوظيف
- شهادة العمل
- بطاقة المنصب

#### 📊 وثائق التقييم (5 أنواع)
- التنقيط الشهري
- التنقيط الفصلي
- التنقيط السنوي
- تقرير التقييم
- بطاقة التقييم

#### 👤 وثائق شخصية (6 أنواع)
- الصورة الشخصية
- نسخة من بطاقة الهوية
- شهادة الميلاد
- شهادة الإقامة
- شهادة الجنسية
- دفتر العائلة

#### 🎓 وثائق تعليمية (5 أنواع)
- الشهادة الجامعية
- شهادة التكوين
- شهادة الخبرة
- كشف النقاط
- شهادة التربص

#### 🏥 وثائق طبية (5 أنواع)
- الفحص الطبي
- شهادة اللياقة البدنية
- التقرير الطبي
- شهادة التلقيح
- التحاليل الطبية

#### 📋 وثائق إدارية (6 أنواع)
- طلب الإجازة
- قرار الترقية
- قرار النقل
- المراسلات الإدارية
- طلب إداري
- العقوبة التأديبية

#### 💰 وثائق مالية (4 أنواع)
- قسيمة الراتب
- شهادة الراتب
- التصريح الضريبي
- كشف الحساب البنكي

#### 🛡️ وثائق التأمين (3 أنواع)
- بطاقة الضمان الاجتماعي
- شهادة الانتساب
- بوليصة التأمين

#### 📚 وثائق التكوين (3 أنواع)
- خطة التكوين
- شهادة المشاركة
- تقييم التكوين

## 🌐 الوصول للنظام

### الروابط الرئيسية:
- **الصفحة الرئيسية**: http://localhost/Edocs/
- **تسجيل الدخول**: http://localhost/Edocs/pages/login.php
- **إعداد النظام**: http://localhost/Edocs/setup.php
- **استيراد البيانات**: http://localhost/Edocs/includes/import_excel.php

### أدوات الإدارة:
- **phpMyAdmin**: http://localhost/phpmyadmin
- **XAMPP Control**: C:\xampp\xampp-control.exe

## 🔧 التخصيص والتطوير

### إضافة أنواع وثائق جديدة:
```sql
INSERT INTO document_types (type_name, type_name_arabe, description, category, is_required) 
VALUES ('نوع جديد', 'نوع جديد بالعربية', 'وصف الوثيقة', 'الفئة', FALSE);
```

### إضافة مستخدم جديد:
```sql
INSERT INTO users (username, password_hash, full_name, role) 
VALUES ('username', '$2y$10$...', 'الاسم الكامل', 'hr_employee');
```

### تخصيص الصلاحيات:
```php
// في ملف includes/auth.php
$permissions = [
    'custom_role' => ['view_employees', 'add_document']
];
```

## 📈 الأداء والتحسين

### للمؤسسات الكبيرة:
- **قاعدة البيانات**: فهارس محسنة، تقسيم الجداول
- **التخزين**: Amazon S3 للملفات الكبيرة
- **الأداء**: Redis للتخزين المؤقت
- **الأمان**: HTTPS، نسخ احتياطية

### مراقبة النظام:
- **السجلات**: logs/error.log
- **قاعدة البيانات**: جدول audit_log
- **الأداء**: مراقبة استخدام الذاكرة والمعالج

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### "خطأ في الاتصال بقاعدة البيانات"
```bash
الحل:
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات config/database.php
3. تأكد من وجود قاعدة البيانات
```

#### "فشل في رفع الملف"
```bash
الحل:
1. تحقق من صلاحيات مجلد uploads
2. تأكد من حجم الملف (أقل من 10MB)
3. تحقق من نوع الملف المسموح
```

#### "Python غير متوفر"
```bash
الحل:
1. ثبت Python مع pandas
2. تأكد من إضافة Python إلى PATH
3. اختبر: python -c "import pandas"
```

## 📞 الدعم والصيانة

### للحصول على المساعدة:
1. راجع ملفات السجلات
2. تحقق من متطلبات النظام
3. راجع الدليل التفصيلي في README.md

### التحديثات المستقبلية:
- إضافة لوحة تحكم متقدمة
- نظام التقارير والإحصائيات
- تطبيق موبايل
- تكامل مع أنظمة خارجية

---

## ✅ قائمة التحقق النهائية

- [ ] XAMPP مثبت ويعمل (Apache + MySQL)
- [ ] Python مثبت مع pandas
- [ ] قاعدة البيانات dossier_employes مُنشأة
- [ ] 6 جداول مُنشأة بنجاح
- [ ] 42 نوع وثيقة مُدرج
- [ ] 3 مستخدمين افتراضيين مُنشأين
- [ ] 1,062 موظف مستورد من Excel
- [ ] مجلدات uploads مُنشأة مع الحماية
- [ ] يمكن الوصول للنظام عبر المتصفح
- [ ] تم تغيير كلمات المرور الافتراضية

**🎉 إذا تم تحقيق جميع النقاط، فالنظام جاهز للاستخدام الكامل!**

---

**المطور**: نظام إدارة الوثائق الإلكترونية  
**الإصدار**: 1.0.0  
**التاريخ**: سبتمبر 2025  
**المؤسسة**: direction des oeuvres universitaires msila

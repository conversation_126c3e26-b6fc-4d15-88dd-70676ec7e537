<?php
/**
 * تحميل الوثيقة
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('view_documents')) {
    http_response_code(403);
    die('غير مصرح لك بهذه العملية');
}

// التحقق من معرف الوثيقة
$document_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($document_id <= 0) {
    http_response_code(400);
    die('معرف الوثيقة غير صحيح');
}

try {
    $db = Database::getInstance();
    
    // جلب تفاصيل الوثيقة
    $document = $db->fetchOne("
        SELECT d.*, 
               dt.type_name_arabe,
               e.nom, e.prenom, e.matricule
        FROM documents d
        JOIN document_types dt ON d.document_type_id = dt.id
        JOIN employees e ON d.employee_id = e.id
        WHERE d.id = ?
    ", [$document_id]);
    
    if (!$document) {
        http_response_code(404);
        die('الوثيقة غير موجودة');
    }
    
    // التحقق من صلاحية الوصول للوثيقة
    if (!canDownloadDocument($document)) {
        http_response_code(403);
        die('غير مصرح لك بتحميل هذه الوثيقة');
    }
    
    // مسار الملف
    $file_path = UPLOAD_PATH . $document['file_path'];
    
    // التحقق من وجود الملف
    if (!file_exists($file_path)) {
        http_response_code(404);
        die('ملف الوثيقة غير موجود');
    }
    
    // تسجيل عملية التحميل
    logActivity(
        'document_downloaded',
        'documents',
        $document_id,
        [
            'employee_id' => $document['employee_id'],
            'employee_name' => $document['nom'] . ' ' . $document['prenom'],
            'employee_matricule' => $document['matricule'],
            'document_type' => $document['type_name_arabe'],
            'filename' => $document['filename'],
            'file_size' => formatFileSize($document['file_size']),
            'download_ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]
    );
    
    // إعداد اسم الملف للتحميل
    $employee_name = sanitizeFilename($document['nom'] . '_' . $document['prenom']);
    $document_type = sanitizeFilename($document['type_name_arabe']);
    $file_extension = pathinfo($document['filename'], PATHINFO_EXTENSION);
    
    $download_filename = "{$employee_name}_{$document_type}_{$document['matricule']}.{$file_extension}";
    
    // تحديد نوع المحتوى
    $content_type = $document['file_type'];
    if (empty($content_type)) {
        $content_type = mime_content_type($file_path);
    }
    
    // إعداد headers للتحميل
    header('Content-Type: ' . $content_type);
    header('Content-Disposition: attachment; filename="' . $download_filename . '"');
    header('Content-Length: ' . $document['file_size']);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // منع التخزين المؤقت للملفات الحساسة
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    
    // قراءة وإرسال الملف
    $file_handle = fopen($file_path, 'rb');
    if ($file_handle === false) {
        http_response_code(500);
        die('خطأ في قراءة الملف');
    }
    
    // إرسال الملف بأجزاء لتوفير الذاكرة
    while (!feof($file_handle)) {
        $chunk = fread($file_handle, 8192); // 8KB chunks
        echo $chunk;
        
        // تنظيف buffer الإخراج
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    fclose($file_handle);
    
} catch (Exception $e) {
    error_log("خطأ في تحميل الوثيقة: " . $e->getMessage());
    
    http_response_code(500);
    die('حدث خطأ في تحميل الوثيقة');
}

/**
 * التحقق من صلاحية تحميل الوثيقة
 */
function canDownloadDocument($document) {
    // المدير يمكنه تحميل جميع الوثائق
    if (hasRole('admin')) {
        return true;
    }
    
    // مسؤول الموارد البشرية يمكنه تحميل جميع الوثائق
    if (hasRole('hr_manager')) {
        return true;
    }
    
    // موظف الموارد البشرية يمكنه تحميل الوثائق
    if (hasRole('hr_employee')) {
        return true;
    }
    
    // مسؤول الأرشيف يمكنه تحميل الوثائق
    if (hasRole('archivist')) {
        return true;
    }
    
    // المستخدم العادي يمكنه تحميل الوثائق العامة فقط
    if (hasRole('viewer')) {
        // يمكن إضافة منطق إضافي هنا للتحكم في التحميل
        return true;
    }
    
    return false;
}

/**
 * تنظيف اسم الملف
 */
function sanitizeFilename($filename) {
    // إزالة الأحرف غير المرغوب فيها
    $filename = preg_replace('/[^a-zA-Z0-9\u0600-\u06FF\s\-_.]/', '', $filename);
    
    // استبدال المسافات بـ underscore
    $filename = preg_replace('/\s+/', '_', $filename);
    
    // إزالة underscore المتكررة
    $filename = preg_replace('/_+/', '_', $filename);
    
    // إزالة underscore من البداية والنهاية
    $filename = trim($filename, '_');
    
    return $filename;
}
?>

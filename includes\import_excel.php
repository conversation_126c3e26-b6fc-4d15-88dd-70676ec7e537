<?php
/**
 * استيراد بيانات الموظفين من ملف Excel
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once 'auth.php';

// التحقق من الصلاحيات
requirePermission('add_employee');

/**
 * كلاس استيراد البيانات من Excel
 */
class ExcelImporter {
    
    /**
     * استيراد الموظفين من ملف Excel الموجود
     */
    public static function importFromExistingFile() {
        try {
            // التحقق من وجود Python و pandas
            $python_check = shell_exec('python -c "import pandas; print(\'OK\')" 2>&1');
            if (strpos($python_check, 'OK') === false) {
                throw new Exception('Python أو مكتبة pandas غير متوفرة');
            }
            
            // مسار ملف Excel
            $excel_file = ROOT_PATH . '/Dossiers_Employes.xlsx';
            
            if (!file_exists($excel_file)) {
                throw new Exception('ملف Excel غير موجود');
            }
            
            // تشغيل سكريبت Python لقراءة البيانات
            $python_script = self::generatePythonScript($excel_file);
            $temp_script = TEMP_PATH . 'import_script.py';
            file_put_contents($temp_script, $python_script);
            
            // تنفيذ السكريبت
            $output = shell_exec("python \"$temp_script\" 2>&1");
            
            // حذف الملف المؤقت
            unlink($temp_script);
            
            if (strpos($output, 'ERROR:') !== false) {
                throw new Exception('خطأ في قراءة ملف Excel: ' . $output);
            }
            
            // قراءة البيانات من ملف JSON المؤقت
            $json_file = TEMP_PATH . 'employees_data.json';
            if (!file_exists($json_file)) {
                throw new Exception('فشل في إنشاء ملف البيانات المؤقت');
            }
            
            $employees_data = json_decode(file_get_contents($json_file), true);
            unlink($json_file);
            
            if (empty($employees_data)) {
                throw new Exception('لا توجد بيانات للاستيراد');
            }
            
            // استيراد البيانات إلى قاعدة البيانات
            return self::importEmployeesToDatabase($employees_data);
            
        } catch (Exception $e) {
            logError("Excel import error: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * توليد سكريبت Python لقراءة Excel
     */
    private static function generatePythonScript($excel_file) {
        $json_output = TEMP_PATH . 'employees_data.json';
        
        return "
import pandas as pd
import json
import sys
from datetime import datetime

try:
    # قراءة ملف Excel
    df = pd.read_excel('$excel_file')
    
    # استخراج الرؤوس من الصف الأول
    headers = df.iloc[0].values
    
    # إنشاء DataFrame جديد مع الرؤوس الصحيحة
    df_clean = df.iloc[1:].copy()
    df_clean.columns = headers
    df_clean = df_clean.reset_index(drop=True)
    
    # تنظيف البيانات وتحويلها
    employees = []
    
    for index, row in df_clean.iterrows():
        try:
            # تنظيف التواريخ
            def clean_date(date_str):
                if pd.isna(date_str) or str(date_str).strip() == '':
                    return None
                try:
                    # محاولة تحويل التاريخ
                    if '/' in str(date_str):
                        return datetime.strptime(str(date_str), '%d/%m/%Y').strftime('%Y-%m-%d')
                    elif '-' in str(date_str):
                        return datetime.strptime(str(date_str), '%Y-%m-%d').strftime('%Y-%m-%d')
                    return None
                except:
                    return None
            
            # تنظيف النصوص
            def clean_text(text):
                if pd.isna(text):
                    return None
                return str(text).strip()
            
            employee = {
                'employee_number': clean_text(row.get('N°')),
                'nin': clean_text(row.get('Identifiant : NIN')),
                'matricule': clean_text(row.get('Matricule')),
                'nom': clean_text(row.get('Nom')),
                'prenom': clean_text(row.get('Prénom')),
                'nom_arabe': clean_text(row.get('Nom Arabe')),
                'prenom_arabe': clean_text(row.get('Prénom Arabe')),
                'date_naissance': clean_date(row.get('Date de naissance')),
                'lieu_naissance': clean_text(row.get('lieu Naissance')),
                'lieu_naissance_arabe': clean_text(row.get('Lieu de naissance arabe')),
                'groupe_sanguin': clean_text(row.get('Groupe Sanguin')),
                'structure': clean_text(row.get('Structure')),
                'type_employe': clean_text(row.get('TYPE')),
                'corps': clean_text(row.get('Corps')),
                'grade': clean_text(row.get('Grade')),
                'filiere': clean_text(row.get('Filière')),
                'echelon': clean_text(row.get('Echelon')),
                'categorie': clean_text(row.get('Catégorie')),
                'position_actuelle': clean_text(row.get('position')),
                'date_recrutement': clean_date(row.get('Date recrutement')),
                'date_installation': clean_date(row.get('Date installation')),
                'numero_securite_sociale': clean_text(row.get('N° sécurité sociale')),
                'numero_compte': clean_text(row.get('N° compte')),
                'carte_rfid': clean_text(row.get('Carte RFID')),
                'photo_status': clean_text(row.get('Photo'))
            }
            
            # التحقق من البيانات الأساسية
            if employee['matricule'] and employee['nom'] and employee['prenom']:
                employees.append(employee)
                
        except Exception as e:
            print(f'خطأ في الصف {index}: {str(e)}', file=sys.stderr)
            continue
    
    # حفظ البيانات في ملف JSON
    with open('$json_output', 'w', encoding='utf-8') as f:
        json.dump(employees, f, ensure_ascii=False, indent=2)
    
    print(f'تم استخراج {len(employees)} موظف بنجاح')
    
except Exception as e:
    print(f'ERROR: {str(e)}', file=sys.stderr)
    sys.exit(1)
";
    }
    
    /**
     * استيراد الموظفين إلى قاعدة البيانات
     */
    private static function importEmployeesToDatabase($employees_data) {
        $db = getDB();
        $imported = 0;
        $updated = 0;
        $errors = [];
        
        try {
            $db->beginTransaction();
            
            foreach ($employees_data as $employee) {
                try {
                    // التحقق من وجود الموظف
                    $existing = dbSelect(
                        "SELECT id FROM employees WHERE matricule = ? OR nin = ?",
                        [$employee['matricule'], $employee['nin']]
                    );
                    
                    if (empty($existing)) {
                        // إضافة موظف جديد
                        $query = "INSERT INTO employees (
                            employee_number, nin, matricule, nom, prenom, nom_arabe, prenom_arabe,
                            date_naissance, lieu_naissance, lieu_naissance_arabe, groupe_sanguin,
                            structure, type_employe, corps, grade, filiere, echelon, categorie,
                            position_actuelle, date_recrutement, date_installation,
                            numero_securite_sociale, numero_compte, carte_rfid, photo_path,
                            status, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)";
                        
                        $photo_path = null;
                        if ($employee['photo_status'] === 'fourni') {
                            $photo_path = 'photos/' . $employee['matricule'] . '.jpg';
                        }
                        
                        $params = [
                            $employee['employee_number'],
                            $employee['nin'],
                            $employee['matricule'],
                            $employee['nom'],
                            $employee['prenom'],
                            $employee['nom_arabe'],
                            $employee['prenom_arabe'],
                            $employee['date_naissance'],
                            $employee['lieu_naissance'],
                            $employee['lieu_naissance_arabe'],
                            $employee['groupe_sanguin'],
                            $employee['structure'],
                            $employee['type_employe'],
                            $employee['corps'],
                            $employee['grade'],
                            $employee['filiere'],
                            $employee['echelon'],
                            $employee['categorie'],
                            $employee['position_actuelle'],
                            $employee['date_recrutement'],
                            $employee['date_installation'],
                            $employee['numero_securite_sociale'],
                            $employee['numero_compte'],
                            $employee['carte_rfid'],
                            $photo_path,
                            getCurrentUser()['id']
                        ];
                        
                        if (dbExecute($query, $params)) {
                            $imported++;
                            logActivity(getCurrentUser()['id'], 'import_employee', 'employees', dbLastInsertId(), $employee);
                        }
                        
                    } else {
                        // تحديث موظف موجود
                        $employee_id = $existing[0]['id'];
                        
                        $query = "UPDATE employees SET 
                            nom = ?, prenom = ?, nom_arabe = ?, prenom_arabe = ?,
                            date_naissance = ?, lieu_naissance = ?, lieu_naissance_arabe = ?,
                            groupe_sanguin = ?, structure = ?, type_employe = ?, corps = ?,
                            grade = ?, filiere = ?, echelon = ?, categorie = ?,
                            position_actuelle = ?, date_recrutement = ?, date_installation = ?,
                            numero_securite_sociale = ?, numero_compte = ?, carte_rfid = ?,
                            updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?";
                        
                        $params = [
                            $employee['nom'],
                            $employee['prenom'],
                            $employee['nom_arabe'],
                            $employee['prenom_arabe'],
                            $employee['date_naissance'],
                            $employee['lieu_naissance'],
                            $employee['lieu_naissance_arabe'],
                            $employee['groupe_sanguin'],
                            $employee['structure'],
                            $employee['type_employe'],
                            $employee['corps'],
                            $employee['grade'],
                            $employee['filiere'],
                            $employee['echelon'],
                            $employee['categorie'],
                            $employee['position_actuelle'],
                            $employee['date_recrutement'],
                            $employee['date_installation'],
                            $employee['numero_securite_sociale'],
                            $employee['numero_compte'],
                            $employee['carte_rfid'],
                            $employee_id
                        ];
                        
                        if (dbExecute($query, $params)) {
                            $updated++;
                            logActivity(getCurrentUser()['id'], 'update_employee', 'employees', $employee_id, $employee);
                        }
                    }
                    
                } catch (Exception $e) {
                    $errors[] = "خطأ في استيراد الموظف {$employee['nom']} {$employee['prenom']}: " . $e->getMessage();
                }
            }
            
            $db->commit();
            
            $message = "تم استيراد $imported موظف جديد وتحديث $updated موظف موجود";
            if (!empty($errors)) {
                $message .= ". الأخطاء: " . implode(', ', array_slice($errors, 0, 5));
            }
            
            return [
                'success' => true,
                'message' => $message,
                'imported' => $imported,
                'updated' => $updated,
                'errors' => $errors
            ];
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
}

// إذا تم استدعاء الملف مباشرة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'import') {
    header('Content-Type: application/json');
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
        exit;
    }
    
    $result = ExcelImporter::importFromExistingFile();
    echo json_encode($result);
    exit;
}
?>

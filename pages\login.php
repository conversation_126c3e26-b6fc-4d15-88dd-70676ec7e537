<?php
/**
 * صفحة تسجيل الدخول
 * نظام إدارة الوثائق الإلكترونية للموظفين
 */

require_once '../config/config.php';
require_once '../includes/auth.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'رمز الأمان غير صحيح';
    } elseif (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $result = Auth::login($username, $password);
        
        if ($result['success']) {
            header('Location: dashboard.php');
            exit;
        } else {
            $error_message = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        
        .login-form {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(45deg, #3498db, #2c3e50);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px 0 0 10px;
        }
        
        .form-control.with-icon {
            border-radius: 0 10px 10px 0;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 1rem;
        }
        
        .system-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-container">
                    <!-- رأس الصفحة -->
                    <div class="login-header">
                        <div class="logo mx-auto mb-3">
                            <i class="bi bi-file-earmark-text" style="font-size: 4rem;"></i>
                        </div>
                        <h3 class="mb-2"><?php echo APP_NAME; ?></h3>
                        <p class="mb-0"><?php echo APP_AUTHOR; ?></p>
                        
                        <div class="system-info">
                            <small>
                                <i class="bi bi-shield-check"></i>
                                نظام آمن لإدارة الوثائق الإلكترونية
                            </small>
                        </div>
                    </div>
                    
                    <!-- نموذج تسجيل الدخول -->
                    <div class="login-form">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="bi bi-check-circle"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person"></i>
                                    اسم المستخدم
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    <input type="text" 
                                           class="form-control with-icon" 
                                           id="username" 
                                           name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                           required 
                                           autocomplete="username"
                                           placeholder="أدخل اسم المستخدم">
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i>
                                    كلمة المرور
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control with-icon" 
                                           id="password" 
                                           name="password" 
                                           required 
                                           autocomplete="current-password"
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword"
                                            style="border-radius: 0 10px 10px 0;">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-login">
                                <i class="bi bi-box-arrow-in-right"></i>
                                تسجيل الدخول
                            </button>
                        </form>
                        
                        <!-- معلومات إضافية -->
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                للحصول على حساب جديد، يرجى التواصل مع مدير النظام
                            </small>
                        </div>
                        
                        <!-- حسابات تجريبية -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>حسابات تجريبية:</strong><br>
                                المدير: admin / password<br>
                                مسؤول الموارد البشرية: hr_manager / password<br>
                                مسؤول الأرشيف: archivist / password
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات النظام -->
                <div class="text-center mt-3">
                    <small class="text-white">
                        الإصدار <?php echo APP_VERSION; ?> | 
                        <i class="bi bi-calendar"></i>
                        <?php echo date('Y'); ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        });
        
        // تركيز على حقل اسم المستخدم
        document.getElementById('username').focus();
        
        // منع إرسال النموذج عدة مرات
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = document.querySelector('.btn-login');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري تسجيل الدخول...';
        });
    </script>
</body>
</html>

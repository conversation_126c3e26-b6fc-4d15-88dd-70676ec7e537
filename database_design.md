# تصميم قاعدة البيانات - نظام إدارة الوثائق الإلكترونية للموظفين

## 📋 تحليل البيانات الموجودة
- **عدد الموظفين الحالي:** 1,062 موظف
- **المؤسسة:** direction des oeuvres universitaires msila
- **نوع البيانات:** ملف Excel يحتوي على معلومات شاملة للموظفين

## 🗄️ تصميم قاعدة البيانات (ERD)

### 1. جدول الموظفين (employees)
```sql
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_number VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم الموظف',
    nin VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم الهوية الوطنية',
    matricule VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم التسجيل',
    
    -- المعلومات الشخصية
    nom VARCHAR(100) NOT NULL COMMENT 'اللقب',
    prenom VARCHAR(100) NOT NULL COMMENT 'الاسم',
    nom_arabe VARCHAR(100) COMMENT 'اللقب بالعربية',
    prenom_arabe VARCHAR(100) COMMENT 'الاسم بالعربية',
    
    -- معلومات الميلاد
    date_naissance DATE COMMENT 'تاريخ الميلاد',
    lieu_naissance VARCHAR(100) COMMENT 'مكان الميلاد',
    lieu_naissance_arabe VARCHAR(100) COMMENT 'مكان الميلاد بالعربية',
    
    -- معلومات طبية
    groupe_sanguin VARCHAR(5) COMMENT 'فصيلة الدم',
    
    -- معلومات مهنية
    structure VARCHAR(200) COMMENT 'الهيكل/المؤسسة',
    type_employe VARCHAR(50) COMMENT 'نوع الموظف',
    corps VARCHAR(100) COMMENT 'السلك',
    grade VARCHAR(100) COMMENT 'الرتبة',
    filiere VARCHAR(100) COMMENT 'الفرع',
    echelon VARCHAR(20) COMMENT 'الدرجة',
    categorie VARCHAR(50) COMMENT 'الفئة',
    position_actuelle VARCHAR(100) COMMENT 'المنصب الحالي',
    
    -- تواريخ مهمة
    date_recrutement DATE COMMENT 'تاريخ التوظيف',
    date_installation DATE COMMENT 'تاريخ التنصيب',
    
    -- معلومات مالية
    numero_securite_sociale VARCHAR(20) COMMENT 'رقم الضمان الاجتماعي',
    numero_compte VARCHAR(30) COMMENT 'رقم الحساب',
    date_affiliation DATE COMMENT 'تاريخ الانتساب',
    
    -- معلومات تقنية
    carte_rfid VARCHAR(20) COMMENT 'بطاقة RFID',
    photo_path VARCHAR(500) COMMENT 'مسار الصورة الشخصية',
    
    -- معلومات النظام
    status ENUM('active', 'inactive', 'retired') DEFAULT 'active' COMMENT 'حالة الموظف',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT COMMENT 'المستخدم الذي أضاف السجل',
    
    INDEX idx_nom (nom),
    INDEX idx_prenom (prenom),
    INDEX idx_matricule (matricule),
    INDEX idx_structure (structure),
    INDEX idx_grade (grade),
    INDEX idx_corps (corps),
    INDEX idx_status (status),
    INDEX idx_date_recrutement (date_recrutement)
);
```

### 2. جدول أنواع الوثائق (document_types)
```sql
CREATE TABLE document_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'اسم نوع الوثيقة',
    type_name_arabe VARCHAR(100) COMMENT 'اسم نوع الوثيقة بالعربية',
    description TEXT COMMENT 'وصف نوع الوثيقة',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'هل الوثيقة مطلوبة',
    category VARCHAR(50) COMMENT 'فئة الوثيقة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_category (category)
);
```

### 3. جدول الوثائق (documents)
```sql
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    document_type_id INT NOT NULL,
    
    -- معلومات الوثيقة
    document_title VARCHAR(200) COMMENT 'عنوان الوثيقة',
    document_number VARCHAR(50) COMMENT 'رقم الوثيقة',
    issue_date DATE COMMENT 'تاريخ الإصدار',
    expiry_date DATE COMMENT 'تاريخ انتهاء الصلاحية',
    issuing_authority VARCHAR(100) COMMENT 'جهة الإصدار',
    
    -- معلومات الملف
    file_name VARCHAR(255) NOT NULL COMMENT 'اسم الملف الأصلي',
    file_path VARCHAR(500) NOT NULL COMMENT 'مسار الملف',
    file_size INT COMMENT 'حجم الملف بالبايت',
    file_type VARCHAR(10) COMMENT 'نوع الملف (PDF, JPG, PNG)',
    file_hash VARCHAR(64) COMMENT 'hash للملف للتحقق من التكامل',
    
    -- معلومات إضافية
    notes TEXT COMMENT 'ملاحظات',
    is_verified BOOLEAN DEFAULT FALSE COMMENT 'هل تم التحقق من الوثيقة',
    verification_date DATE COMMENT 'تاريخ التحقق',
    verified_by INT COMMENT 'المستخدم الذي تحقق من الوثيقة',
    
    -- معلومات النظام
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    uploaded_by INT NOT NULL COMMENT 'المستخدم الذي رفع الوثيقة',
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (verified_by) REFERENCES users(id),
    
    INDEX idx_employee_id (employee_id),
    INDEX idx_document_type (document_type_id),
    INDEX idx_issue_date (issue_date),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

### 4. جدول المستخدمين (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    
    -- معلومات شخصية
    full_name VARCHAR(100) NOT NULL,
    full_name_arabe VARCHAR(100),
    
    -- صلاحيات
    role ENUM('admin', 'hr_manager', 'hr_employee', 'archivist', 'viewer') NOT NULL DEFAULT 'viewer',
    permissions JSON COMMENT 'صلاحيات مخصصة',
    
    -- معلومات الحساب
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    
    -- معلومات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active)
);
```

### 5. جدول سجل العمليات (audit_log)
```sql
CREATE TABLE audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(50) NOT NULL COMMENT 'نوع العملية',
    table_name VARCHAR(50) COMMENT 'اسم الجدول المتأثر',
    record_id INT COMMENT 'معرف السجل المتأثر',
    old_values JSON COMMENT 'القيم القديمة',
    new_values JSON COMMENT 'القيم الجديدة',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);
```

### 6. جدول الجلسات (sessions)
```sql
CREATE TABLE sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    payload TEXT NOT NULL,
    last_activity INT NOT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);
```

## 🔗 العلاقات بين الجداول

1. **employees → documents** (One-to-Many): موظف واحد يمكن أن يملك عدة وثائق
2. **document_types → documents** (One-to-Many): نوع وثيقة واحد يمكن أن يكون لعدة وثائق
3. **users → documents** (One-to-Many): مستخدم واحد يمكن أن يرفع عدة وثائق
4. **users → audit_log** (One-to-Many): مستخدم واحد يمكن أن يقوم بعدة عمليات
5. **users → sessions** (One-to-Many): مستخدم واحد يمكن أن يملك عدة جلسات

## 📊 الفهارس المقترحة للأداء

```sql
-- فهارس مركبة للبحث السريع
CREATE INDEX idx_employee_search ON employees(nom, prenom, matricule);
CREATE INDEX idx_employee_professional ON employees(structure, grade, corps);
CREATE INDEX idx_document_search ON documents(employee_id, document_type_id, status);
CREATE INDEX idx_document_dates ON documents(issue_date, expiry_date);

-- فهرس نصي للبحث الشامل
ALTER TABLE employees ADD FULLTEXT(nom, prenom, nom_arabe, prenom_arabe);
ALTER TABLE documents ADD FULLTEXT(document_title, notes);
```

## 🎯 أنواع الوثائق المقترحة

سيتم إدراج هذه الأنواع في جدول document_types:

1. **وثائق التوظيف**
   - محضر التنصيب
   - عقد العمل
   - قرار التوظيف
   - شهادة العمل

2. **وثائق التقييم**
   - التنقيط الشهري
   - التنقيط الفصلي
   - التنقيط السنوي
   - تقرير الأداء

3. **وثائق شخصية**
   - صورة شخصية
   - نسخة من الهوية الوطنية
   - شهادة الميلاد
   - شهادة الإقامة

4. **وثائق تعليمية**
   - الشهادات الجامعية
   - شهادات التكوين
   - شهادات الخبرة

5. **وثائق طبية**
   - الفحص الطبي
   - شهادة اللياقة البدنية
   - تقارير طبية

6. **وثائق إدارية**
   - طلبات الإجازة
   - قرارات الترقية
   - قرارات النقل
   - المراسلات الإدارية
